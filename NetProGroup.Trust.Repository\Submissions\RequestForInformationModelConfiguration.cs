// <copyright file="RequestForInformationModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.Repository.Submissions
{
    /// <summary>
    /// Model configuration for request for information.
    /// </summary>
    public class RequestForInformationModelConfiguration : IEntityTypeConfiguration<RequestForInformation>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<RequestForInformation> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "RequestForInformation", TrustDbContext.DbSchema);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            builder.Property(x => x.CancellationReason)
                .HasMaxLength(1000);

            // Submission relationship (required)
            builder.HasOne(rfi => rfi.Submission).WithMany(s => s.RequestsForInformation)
                .HasForeignKey(rfi => rfi.SubmissionId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_RequestForInformation_Submission");

            builder.HasOne(s => s.CreatedByUser).WithMany()
                .HasForeignKey(s => s.CreatedBy)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_RequestForInformation_ApplicationUser");

            builder.HasOne(s => s.RepliedByUser).WithMany()
                .HasForeignKey(s => s.RepliedBy)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_RequestForInformation_Users_RepliedBy");

            builder.HasOne(s => s.CompletedByUser).WithMany()
                .HasForeignKey(s => s.CompletedBy)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_RequestForInformation_Users_CompletedBy");
        }
    }
}
