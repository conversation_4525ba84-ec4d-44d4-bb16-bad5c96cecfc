﻿// <copyright file="IFormsDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Application.Contracts.Forms;
using NetProGroup.Trust.DataManager.Forms.RequestResponses;

namespace NetProGroup.Trust.DataManager.Forms
{
    /// <summary>
    /// Interface for the datamanager for forms.
    /// </summary>
    public interface IFormsDataManager : IScopedService
    {
        /// <summary>
        /// Gets a paged list with form templates.
        /// </summary>
        /// <param name="request">Request with optional parameters to search for.</param>
        /// <returns>A <see cref="Task{ListFormTemplatesResponse}"/> representing the result of the asynchronous operation.</returns>
        Task<ListFormTemplatesResponse> ListFormTemplatesAsync(ListFormTemplatesRequest request);

        /// <summary>
        /// Gets a paged list with form documents.
        /// </summary>
        /// <param name="request">Request with optional parameters to search for.</param>
        /// <returns>A <see cref="Task{ListFormDocumentsResponse}"/> representing the result of the asynchronous operation.</returns>
        Task<ListFormDocumentsResponse> ListFormDocumentsAsync(ListFormDocumentsRequest request);

        /// <summary>
        /// Gets a specific FormTemplate.
        /// </summary>
        /// <param name="request">Request with the parameters to get the from template.</param>
        /// <returns>A <see cref="Task{GetFormTemplateResponse}"/> representing the result of the asynchronous operation.</returns>
        Task<GetFormTemplateResponse> GetFormTemplateAsync(GetFormTemplateRequest request);

        /// <summary>
        /// Gets a specific FormDocument.
        /// </summary>
        /// <param name="request">Request with the parameters to get the form document.</param>
        /// <returns>A <see cref="Task{GetFormDocumentResponse}"/> representing the result of the asynchronous operation.</returns>
        Task<GetFormDocumentResponse> GetFormDocumentAsync(GetFormDocumentRequest request);

        /// <summary>
        /// Creates a new FormDocument based on the given template. If no Form specified, the data of the template is read and used.
        /// </summary>
        /// <param name="request">Request with the data for the new FormDocument.</param>
        /// <returns>A <see cref="Task{GetFormDocumentResponse}"/> representing the result of the asynchronous operation.</returns>
        Task<GetFormDocumentResponse> CreateFormDocumentAsync(CreateFormDocumentRequest request);

        /// <summary>
        /// Creates a new version for a form template.
        /// </summary>
        /// <param name="model">The model with all parameters for the new version.</param>
        /// <returns>The created form template.</returns>
        Task<FormTemplateWithVersionsDTO> CreateFormTemplateVersionAsync(CreateFormTemplateVersionDTO model);

        /// <summary>
        /// Updates a version of a form template.
        /// </summary>
        /// <param name="model">The model with all parameters for the version.</param>
        /// <returns>The updated form template.</returns>
        Task<FormTemplateWithVersionsDTO> UpdateFormTemplateVersionAsync(UpdateFormTemplateVersionDTO model);

        /// <summary>
        /// Creates a set of formtemplates for the jurisdiction/module for the given years.
        /// </summary>
        /// <param name="jurisdictionId">The jurisdiction to generate for.</param>
        /// <param name="moduleKey">Module to use.</param>
        /// <param name="years">Years to generate.</param>
        /// <returns>An awaitable task.</returns>
        Task CreateFormTemplatesForJurisdictionAsync(Guid jurisdictionId, string moduleKey, params int[] years);

        /// <summary>
        /// Checks if the form template exists.
        /// </summary>
        /// <param name="formTemplateId">The ID of the form template to check.</param>
        /// <returns>The template or throws an exception.</returns>
        /// <exception cref="NotFoundException">Thrown when the entity cannot be found.</exception>
        Task<FormTemplateDTO> CheckFormTemplateByIdAsync(Guid formTemplateId);
    }
}