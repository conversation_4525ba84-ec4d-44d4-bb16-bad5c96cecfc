﻿// <copyright file="SubmissionsManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.EF.Repository.Interfaces;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Documents;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Forms;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Payments.Invoices;
using NetProGroup.Trust.DataManager.Payments.Invoices.RequestResponses;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Permissions;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Forms;
using NetProGroup.Trust.Shared.FormDocuments;
using NetProGroup.Trust.Shared.Jurisdictions;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Linq.Expressions;
using X.PagedList;
using KeyValueForm = NetProGroup.Trust.Forms.Forms.KeyValueForm;

namespace NetProGroup.Trust.DataManager.Submissions
{
    /// <summary>
    /// Manager for handling submissions.
    /// </summary>
    [SuppressMessage("Design", "CA1031:Do not catch general exception types", Justification = "Ignore")]
    [SuppressMessage("StyleCop.CSharp.OrderingRules", "SA1202:Elements should be ordered by access", Justification = "Ignore")]
    [SuppressMessage("Globalization", "CA1308:Normalize strings to uppercase", Justification = "Ignore")]
    public class SubmissionsManager : ISubmissionsManager
    {
        private readonly ILogger _logger;
        private readonly IWorkContext _workContext;
        private readonly IMapper _mapper;
        private readonly IDateTimeProvider _dateTimeProvider;
        private readonly IDocumentManager _documentManager;
        private readonly ILegalEntitiesRepository _legalEntitiesRepository;
        private readonly IFormDocumentDocumentsRepository _formDocumentDocumentsRepository;
        private readonly IFormTemplatesRepository _formTemplatesRepository;
        private readonly ISubmissionsRepository _submissionsExcludingDeletedRepository;
        private readonly ISubmissionsIncludingDeletedRepository _submissionsIncludingDeletedRepository;
        private readonly IFormDocumentsRepository _formDocumentsRepository;
        private readonly IFormDocumentRevisionsRepository _formDocumentRevisionsRepository;
        private readonly IFormDocumentAttributesRepository _formDocumentAttributesRepository;

        private readonly IModulesRepository _modulesRepository;
        private readonly ISystemAuditManager _systemAuditManager;

        private readonly ISettingsManager _settingsManager;
        private readonly IInvoiceDataManager _invoicesManager;
        private readonly IAuthorizationFilterExpressionFactory _authorizationFilterExpressionFactory;
        private readonly ISubmissionsDataManagerFactory _submissionsDataManagerFactory;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionsManager"/> class.
        /// </summary>
        /// <param name="logger">Logger instance.</param>
        /// <param name="workContext">Instance of the current WorkContext.</param>
        /// <param name="mapper">The mapper instance.</param>
        /// <param name="dateTimeProvider">The date time provider.</param>
        /// <param name="documentManager">An instance of IDocumentManager.</param>
        /// <param name="legalEntitiesRepository">Repository for legal entities.</param>
        /// <param name="formTemplatesRepository">Repository for form templates.</param>
        /// <param name="formDocumentsRepository">Repository for formDocument.</param>
        /// <param name="formDocumentDocumentsRepository">Repository for form document documents.</param>
        /// <param name="formDocumentRevisionsRepository">Repository for form document revisions.</param>
        /// <param name="formDocumentAttributesRepository">Repository for form documents attributes.</param>
        /// <param name="modulesRepository">Repository for modules.</param>
        /// <param name="jurisdictionsRepository">Repository for jurisdictions.</param>
        /// <param name="submissionsExcludingDeletedRepository">Repository for submissions.</param>
        /// <param name="systemAuditManager">Manager for system audits.</param>
        /// <param name="settingsManager">Manager for settings.</param>
        /// <param name="invoicesManager">Manager for invoices.</param>
        /// <param name="authorizationFilterExpressionFactory">Factory for authorization filter expressions.</param>
        /// <param name="submissionsDataManagerFactory">Factory for jurisdiction and/or module specific SubmissionsDataManager.</param>
        /// <param name="submissionsIncludingDeletedRepository">Repository for submissions including deleted ones.</param>
        /// <param name="securityManager">The security manager.</param>
        public SubmissionsManager(ILogger<SubmissionsManager> logger,
                                  IWorkContext workContext,
                                  IMapper mapper,
                                  IDateTimeProvider dateTimeProvider,
                                  IDocumentManager documentManager,
                                  ILegalEntitiesRepository legalEntitiesRepository,
                                  IFormTemplatesRepository formTemplatesRepository,
                                  IFormDocumentsRepository formDocumentsRepository,
                                  IFormDocumentDocumentsRepository formDocumentDocumentsRepository,
                                  IFormDocumentRevisionsRepository formDocumentRevisionsRepository,
                                  IFormDocumentAttributesRepository formDocumentAttributesRepository,
                                  IModulesRepository modulesRepository,
                                  IJurisdictionsRepository jurisdictionsRepository,
                                  ISubmissionsRepository submissionsExcludingDeletedRepository,
                                  ISystemAuditManager systemAuditManager,
                                  ISettingsManager settingsManager,
                                  IInvoiceDataManager invoicesManager,
                                  IAuthorizationFilterExpressionFactory authorizationFilterExpressionFactory,
                                  ISubmissionsDataManagerFactory submissionsDataManagerFactory,
                                  ISubmissionsIncludingDeletedRepository submissionsIncludingDeletedRepository,
                                  ISecurityManager securityManager)
        {
            _logger = logger;
            _workContext = workContext;
            _mapper = mapper;
            _dateTimeProvider = dateTimeProvider;
            _documentManager = documentManager;

            _legalEntitiesRepository = legalEntitiesRepository;
            _formDocumentsRepository = formDocumentsRepository;
            _formDocumentDocumentsRepository = formDocumentDocumentsRepository;
            _formDocumentRevisionsRepository = formDocumentRevisionsRepository;
            _formDocumentAttributesRepository = formDocumentAttributesRepository;
            _formTemplatesRepository = formTemplatesRepository;
            _modulesRepository = modulesRepository;
            _submissionsExcludingDeletedRepository = submissionsExcludingDeletedRepository;
            _systemAuditManager = systemAuditManager;
            _settingsManager = settingsManager;
            _invoicesManager = invoicesManager;
            _authorizationFilterExpressionFactory = authorizationFilterExpressionFactory;
            _submissionsDataManagerFactory = submissionsDataManagerFactory;
            _submissionsIncludingDeletedRepository = submissionsIncludingDeletedRepository;
            _securityManager = securityManager;
        }

        /// <inheritdoc/>
        public async Task<SubmissionsPaidStatusResponse> GetSubmissionsPaidStatusByCompanyAndYearAsync(IEnumerable<CompanyFinancialYearDto> companyFilingYearDtos,
            Guid moduleId,
            List<Guid> authorizedJurisdictionIDs)
        {
            ArgumentNullException.ThrowIfNull(companyFilingYearDtos, nameof(companyFilingYearDtos));

            var companies = (await _legalEntitiesRepository.FindByConditionAsync(le => companyFilingYearDtos.Select(c => c.CompanyVPCode).Contains(le.Code))).ToList();

            var submissionsForCompanies = (await _submissionsExcludingDeletedRepository.FindByConditionAsync(s => companies.Select(entity => entity.Id).Contains(s.LegalEntityId)))
                .ToList();

            var result = new SubmissionsPaidStatusResponse
            {
                PaidStatuses = companyFilingYearDtos.Select(companyFilingYear =>
                {
                    var company = companies.SingleOrDefault(c => c.Code == companyFilingYear.CompanyVPCode);
                    if (company == null)
                    {
                        _logger.LogError("Company '{CompanyCode}' not found", companyFilingYear.CompanyVPCode);
                        return new SubmissionPaidStatusDto
                        {
                            CompanyVPCode = companyFilingYear.CompanyVPCode,
                            FinancialYear = companyFilingYear.FinancialYear,
                            IsPaid = null,
                            SubmissionAvailable = false
                        };
                    }

                    if (!authorizedJurisdictionIDs.Contains(company.JurisdictionId.GetValueOrDefault()))
                    {
                        throw new ForbiddenException(ApplicationErrors.NO_PERMISSION.ToErrorCode(), $"No access to jurisdiction of company {company.Code}");
                    }

                    var submission = submissionsForCompanies.SingleOrDefault(s =>
                        s.LegalEntityId == company.Id && s.FinancialYear == companyFilingYear.FinancialYear && s.ModuleId == moduleId);
                    return new SubmissionPaidStatusDto
                    {
                        CompanyVPCode = companyFilingYear.CompanyVPCode,
                        FinancialYear = companyFilingYear.FinancialYear,
                        IsPaid = submission?.IsPaid,
                        SubmissionAvailable = submission != null
                    };
                }).ToList()
            };

            return result;
        }

        /// <inheritdoc/>
        public async Task<MarkSubmissionsAsPaidResponse> MarkSubmissionsAsPaidAsync(List<Guid> submissionIds, bool isPaid, List<Guid> authorizedJurisdictionIDs)
        {
            ArgumentNullException.ThrowIfNull(submissionIds, nameof(submissionIds));
            ArgumentNullException.ThrowIfNull(authorizedJurisdictionIDs, nameof(authorizedJurisdictionIDs));

            var submissions = (await _submissionsExcludingDeletedRepository.FindByConditionAsync(s => submissionIds.Contains(s.Id),
                GetIncludesForMarkAsPaid())).ToList();

            var result = new Dictionary<Guid, MarkSubmissionsAsPaidResultDTO>();
            Guid? moduleId = null;
            foreach (var submissionId in submissionIds)
            {
                var submissionResult = new MarkSubmissionsAsPaidResultDTO();
                result.Add(submissionId, submissionResult);

                try
                {
                    var submission = submissions.SingleOrDefault(s => s.Id == submissionId);
                    if (submission == null)
                    {
                        _logger.LogError("Submission '{SubmissionId}' not found", submissionId);
                        throw new NotFoundException(ApplicationErrors.SUBMISSION_ID_NOT_FOUND.ToErrorCode(), "Submission not found");
                    }

                    if (!authorizedJurisdictionIDs.Contains(submission.LegalEntity.JurisdictionId.GetValueOrDefault()))
                    {
                        throw new ForbiddenException(ApplicationErrors.NO_PERMISSION.ToErrorCode(), $"No access to jurisdiction of submission {submissionId}");
                    }

                    if (moduleId == null)
                    {
                        moduleId = submission.ModuleId.Value;
                    }
                    else if (moduleId != submission.ModuleId)
                    {
                        throw new BadRequestException(ApplicationErrors.MULTIPLE_MODULES_NOT_ALLOWED.ToErrorCode(), "Multiple modules not allowed");
                    }

                    await MarkSubmissionAsPaid(isPaid, submission);
                }
                catch (Exception ex)
                {
                    submissionResult.ErrorMessage = ex.Message;
                }
            }

            await _submissionsExcludingDeletedRepository.SaveChangesAsync();

            return new MarkSubmissionsAsPaidResponse() { Results = result };
        }

        /// <inheritdoc/>
        public async Task<MarkSubmissionsAsPaidByCompanyYearResponse> MarkSubmissionsAsPaidByCompanyAndYearAsync(IEnumerable<CompanyFinancialYearDto> companyFilingYearDtos,
            bool isPaid,
            Guid moduleId,
            List<Guid> authorizedJurisdictionIDs)
        {
            ArgumentNullException.ThrowIfNull(companyFilingYearDtos, nameof(companyFilingYearDtos));
            ArgumentNullException.ThrowIfNull(authorizedJurisdictionIDs, nameof(authorizedJurisdictionIDs));

            var companies = (await _legalEntitiesRepository.FindByConditionAsync(le => companyFilingYearDtos.Select(c => c.CompanyVPCode).Contains(le.Code),
                                                                                 q => q.Include(le => le.AnnualFees))).ToList();

            var submissionsForCompanies = (await _submissionsExcludingDeletedRepository.FindByConditionAsync(
                    s => companies.Select(entity => entity.Id).Contains(s.LegalEntityId) &&
                         s.ModuleId == moduleId,
                    GetIncludesForMarkAsPaid()))
                .ToList();

            var result = new List<MarkSubmissionsAsPaidByCompanyAndYearResultDTO>();
            foreach (var companyFilingYear in companyFilingYearDtos)
            {
                var submissionResult = new MarkSubmissionsAsPaidByCompanyAndYearResultDTO()
                {
                    CompanyVPCode = companyFilingYear.CompanyVPCode,
                    FinancialYear = companyFilingYear.FinancialYear,
                    ErrorMessage = null
                };

                result.Add(submissionResult);

                try
                {
                    var company = companies.SingleOrDefault(c => c.Code == companyFilingYear.CompanyVPCode);
                    if (company == null)
                    {
                        _logger.LogError("Company '{CompanyCode}' not found", companyFilingYear.CompanyVPCode);
                        throw new NotFoundException(ApplicationErrors.COMPANY_NOT_FOUND.ToErrorCode(), "Company not found");
                    }

                    if (!authorizedJurisdictionIDs.Contains(company.JurisdictionId.GetValueOrDefault()))
                    {
                        throw new ForbiddenException(ApplicationErrors.NO_PERMISSION.ToErrorCode(), $"No access to jurisdiction of company {company.Code}");
                    }

                    var submission = submissionsForCompanies.SingleOrDefault(s =>
                        s.LegalEntityId == company.Id && s.FinancialYear == companyFilingYear.FinancialYear && s.ModuleId == moduleId);

                    if (submission == null)
                    {
                        _logger.LogError("Submission for company '{CompanyCode}' and year '{FinancialYear}' not found", companyFilingYear.CompanyVPCode, companyFilingYear.FinancialYear);
                        throw new NotFoundException(ApplicationErrors.SUBMISSION_ID_NOT_FOUND.ToErrorCode(), "Submission not found");
                    }

                    await MarkSubmissionAsPaid(isPaid, submission);
                }
                catch (Exception ex)
                {
                    submissionResult.ErrorMessage = ex.Message;
                }
            }

            await _submissionsExcludingDeletedRepository.SaveChangesAsync();

            return new MarkSubmissionsAsPaidByCompanyYearResponse() { Results = result };
        }

        /// <inheritdoc/>
        public async Task MarkSubmissionsAsPaidByInvoiceIdAsync(Guid invoiceId, bool saveChanges = false)
        {
            var submissions = await _submissionsExcludingDeletedRepository.FindByConditionAsync(s => s.InvoiceId == invoiceId);

            foreach (var submission in submissions)
            {
                if (!submission.IsPaid)
                {
                    _logger.LogInformation("Marking submission '{SubmissionId}' as paid", submission.Id);

                    submission.SetPaid(true, _dateTimeProvider.UtcNow);

                    await _submissionsExcludingDeletedRepository.UpdateAsync(submission, saveChanges);
                }
            }
        }

        /// <inheritdoc/>
        public async Task<SubmissionDTO> StartSubmissionAsync(StartSubmissionDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            await _legalEntitiesRepository.CheckLegalEntityByIdAsync(model.LegalEntityId);

            var module = await _modulesRepository.CheckModuleByIdAsync(model.ModuleId);

            var legalEntity = await _legalEntitiesRepository.GetByIdAsync(model.LegalEntityId,
                q => q.Include(le => le.MasterClient)
                    .Include(mc => mc.Jurisdiction));
            var jurisdiction = legalEntity.Jurisdiction;

            var result = new SubmissionDTO();

            // Use the factory to create the dedicated SubmissionsDataManager for the jurisdiction/module
            var submissionsDataManager = _submissionsDataManagerFactory.CreateSubmissionsDataManager(jurisdiction.Code, module.Key);

            if (submissionsDataManager != null)
            {
                result = await submissionsDataManager.StartSubmissionAsync(model, jurisdiction, module);
            }
            else
            {
                // Check for the jurisdiction to perform jurisdiction specific validations
                switch (jurisdiction.Code)
                {
                    case JurisdictionCodes.Nevis:
                        result = await StartSubmissionForNevisAsync(model, jurisdiction, module, model.UserEmail, model.UserId);
                        break;

                    case JurisdictionCodes.Panama:
                    case JurisdictionCodes.Bahamas:

                        result = await StartSubmissionAsync(model, jurisdiction, module, _workContext.User.Email, _workContext.IdentityUserId.Value);
                        break;

                    default:
                        throw new ConstraintException(
                            ApplicationErrors.JURISDICTION_UNKNOWN.ToErrorCode(),
                            $"The jurisdiction '{jurisdiction.Name}' is not configured.");
                }
            }

            return result;
        }

        /// <inheritdoc/>
        public async Task<SubmissionDTO> GetSubmissionAsync(Guid submissionId)
        {
            Check.NotDefaultOrNull<Guid>(submissionId, nameof(submissionId));

            var submission = await _submissionsExcludingDeletedRepository.GetByIdAsync(submissionId, (q) => q.Include(s => s.LegalEntity).ThenInclude(le => le.Jurisdiction));
            var result = _mapper.Map<SubmissionDTO>(submission);

            return result;
        }

        /// <inheritdoc/>
        public async Task<SubmissionDTO> GetSubmissionAsync(Guid submissionId, bool includeFormDocument, bool allowDeleted = false)
        {
            Check.NotDefaultOrNull<Guid>(submissionId, nameof(submissionId));

            IRepository<Submission, Guid> repository = allowDeleted ? _submissionsIncludingDeletedRepository : _submissionsExcludingDeletedRepository;

            // Check for the submission entity.
            var submission = await repository.CheckSubmissionByIdAsync(
                submissionId,
                throwError: true,
                throwNotFound: true,
                options: q => q
                    .Include(s => s.LegalEntity).ThenInclude(le => le.MasterClient)
                    .Include(s => s.LegalEntity).ThenInclude(le => le.Jurisdiction)
                    .Include(s => s.Invoice).ThenInclude(inv => inv.PaymentInvoices).ThenInclude(pi => pi.Payment)
                    .Include(s => s.Attributes)
                    .Include(s => s.SubmittedByUser)
                    .AsSplitQuery());

            FormDocumentRevision formDocumentRevision = null;
            if (includeFormDocument)
            {
                // Get the last revision for the submission
                formDocumentRevision = await _formDocumentRevisionsRepository.FindFirstOrDefaultByConditionAsync(fdr => fdr.FormDocumentId == submission.FormDocumentId,
                                                                                                                 q => q.Include(fdr => fdr.FormDocument)
                                                                                                                       .OrderByDescending(fdr => fdr.Revision));
            }

            var result = _mapper.Map<SubmissionDTO>(submission);

            if (formDocumentRevision != null)
            {
                result.FormDocument = _mapper.Map<FormDocumentWithRevisionsDTO>(formDocumentRevision.FormDocument);
            }

            // Retreive the FormDocumentDocuments if any exists
            var formDocumentDocuments = await _formDocumentDocumentsRepository.FindByConditionAsync(
                fdd => fdd.FormDocumentId == submission.FormDocumentId);

            // Return the document ids relation to the submission
            result.DocumentIds = formDocumentDocuments.Select(fdd => fdd.DocumentId).ToList();

            return result;
        }

        /// <inheritdoc/>
        public async Task UpdateSubmissionExport(IEnumerable<Guid> submissionIds, Guid userId, Guid moduleId)
        {
            ArgumentNullException.ThrowIfNull(submissionIds, nameof(submissionIds));
            ArgumentNullException.ThrowIfNull(userId, nameof(userId));

            var submissions = await _submissionsExcludingDeletedRepository.GetQueryable()
                .Where(s => submissionIds.Contains(s.Id))
                .ToListAsync();

            foreach (var submission in submissions)
            {
                if (submission.ModuleId != moduleId)
                {
                    throw new BadRequestException(ApplicationErrors.MULTIPLE_MODULES_NOT_ALLOWED.ToErrorCode(), "Multiple modules not allowed");
                }

                submission.ExportedBy = userId;
                submission.ExportedAt = _dateTimeProvider.UtcNow;
                await _submissionsExcludingDeletedRepository.UpdateAsync(submission);
            }

            await _submissionsExcludingDeletedRepository.SaveChangesAsync();
        }

        /// <inheritdoc/>
        public async Task<SubmissionDTO> UpdateSubmissionDataSetAsync(SubmissionDataSetDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            // Check for the submission entity.
            var submission = await _submissionsExcludingDeletedRepository.CheckSubmissionByIdAsync(model.Id);

            if (submission.Status == SubmissionStatus.Submitted)
            {
                throw new ConstraintException(ApplicationErrors.SUBMISSION_ALREADY_SUBMITTED.ToErrorCode(), "Submission has status 'Submitted");
            }

            // Get the last revision for the submission
            var formDocumentRevision = await _formDocumentRevisionsRepository.FindFirstOrDefaultByConditionAsync(fdr => fdr.FormDocumentId == submission.FormDocumentId,
                                                                                                                 q => q.Include(fdr => fdr.FormDocument).ThenInclude(fd => fd.Attributes)
                                                                                                                       .OrderByDescending(fdr => fdr.Revision));

            if (formDocumentRevision == null)
            {
                throw new NotFoundException(ApplicationErrors.FORMDOCUMENTREVISION_NOT_FOUND.ToErrorCode(), "No last revision found");
            }

            if (formDocumentRevision.FormDocument.Status == FormDocumentStatus.Finalized)
            {
                throw new ConstraintException(ApplicationErrors.FORMDOCUMENTREVISION_ALREADY_FINALIZED.ToErrorCode(), "Last revision has status 'Finalized");
            }

            // Serialize the data and set the json
            var existingFormBuilder = FormBuilder.FromJson(formDocumentRevision.DataAsJson);
            var theForm = existingFormBuilder.Form as KeyValueForm;
            theForm.DataSet = model.DataSet;

            var json = existingFormBuilder.ToJson();
            formDocumentRevision.DataAsJson = json;

            await UpdateFormAttributes(theForm, formDocumentRevision.FormDocument);

            // Create the FormDocumentDocuments if any exists
            if (model.DocumentIds?.Count > 0)
            {
                await CreateUpdateFormDocumentDocumentsAsync(formDocumentRevision.FormDocumentId, model.DocumentIds);
            }

            await _formDocumentRevisionsRepository.UpdateAsync(formDocumentRevision, saveChanges: true);

            return await GetSubmissionAsync(model.Id, formDocumentRevision.Id, true);
        }

        /// <inheritdoc/>
        public async Task<SubmissionDTO> SubmitSubmissionAsync(SubmitSubmissionDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            // Check for the submission entity.
            var submission = await _submissionsExcludingDeletedRepository.CheckSubmissionByIdAsync(model.SubmissionId);

            if (submission.Status == SubmissionStatus.Submitted)
            {
                throw new ConstraintException(ApplicationErrors.SUBMISSION_ALREADY_SUBMITTED.ToErrorCode(), "Submission has status 'Submitted");
            }

            // Get the last revision for the submission
            var formDocumentRevision = await _formDocumentRevisionsRepository.FindFirstOrDefaultByConditionAsync(fdr => fdr.FormDocumentId == submission.FormDocumentId,
                                                                                                                 q => q.Include(fdr => fdr.FormDocument).ThenInclude(f => f.Attributes)
                                                                                                                       .OrderByDescending(fdr => fdr.Revision));

            if (formDocumentRevision == null)
            {
                throw new NotFoundException(ApplicationErrors.FORMDOCUMENTREVISION_NOT_FOUND.ToErrorCode(), "No last revision found");
            }

            if (formDocumentRevision.FormDocument.Status == FormDocumentStatus.Finalized)
            {
                throw new ConstraintException(ApplicationErrors.FORMDOCUMENTREVISION_ALREADY_FINALIZED.ToErrorCode(), "Last revision has status 'Finalized");
            }

            // Set the current company data on the submission
            var legalEntity = await _legalEntitiesRepository.GetByIdAsync(submission.LegalEntityId,
                q => q.Include(le => le.MasterClient));

            var settings = await _settingsManager.GetDerivedSettingsForCompany<FeeSettingsDTO>(legalEntity.Id);

            KeyValueForm theform = null;
            if (formDocumentRevision.GetFormBuilder().Form is KeyValueForm form)
            {
                theform = form;

                form.DataSet[FormKeys.CompanyName] = legalEntity.Name;
                form.DataSet[FormKeys.CompanyCode] = legalEntity.Code;
                form.DataSet[FormKeys.CompanyIncorporationNumber] = legalEntity.IncorporationNr;
                form.DataSet[FormKeys.CompanyMasterClientCode] = legalEntity.MasterClient.Code;
                form.DataSet[FormKeys.CompanyReferralOffice] = legalEntity.ReferralOffice;
                form.DataSet[FormKeys.CompanyStrSubmissionFee] = settings.STRSubmissionFee.GetValueOrDefault().ToString(CultureInfo.InvariantCulture);
                form.DataSet[FormKeys.CompanyStrSubmissionLatePaymentFeeExempt] = (settings.STRSubmissionLatePaymentFeeExempt == true).ToString();
                form.DataSet[FormKeys.CompanyIsActive] = legalEntity.IsActive.ToString().ToLowerInvariant();

                formDocumentRevision.DataAsJson = form.ToJson();
            }

            // Set the status of the submission, the formdocument and the revision all to finalized.
            formDocumentRevision.Status = FormDocumentRevisionStatus.Finalized;

            formDocumentRevision.FormDocument.Status = FormDocumentStatus.Finalized;
            formDocumentRevision.FormDocument.FinalizedAt = DateTime.UtcNow;

            // Store the current submission status
            var previousSubmissionStatus = submission.Status;

            submission.Status = SubmissionStatus.Submitted;
            submission.SubmittedAt = DateTime.UtcNow;
            submission.SubmittedBy = _workContext.IdentityUserId;

            var tx = await _submissionsExcludingDeletedRepository.DbContext.Database.BeginTransactionAsync(System.Data.IsolationLevel.RepeatableRead);

            try
            {
                // Need to take some additional actions here?
                // Like creating an invoice?
                // Make sure we use a transaction
                if (!submission.InvoiceId.HasValue && !submission.IsPaid)
                {
                    await CreateInvoiceForSTRSubmissionAsync(submission);
                }

                if (previousSubmissionStatus == SubmissionStatus.Draft)
                {
                    // Save the initial submitted date as a SubmissionAttribute
                    submission.Attributes.SetAttributeValue<DateTime>(SubmissionAttributeKeys.InitialSubmittedAt, DateTime.UtcNow);
                }

                await _systemAuditManager.AddSubmissionSubmittedActivityLogAsync(submission, previousSubmissionStatus);

                await UpdateFormAttributes(theform, formDocumentRevision.FormDocument);

                await _formDocumentRevisionsRepository.SaveChangesAsync();

                await tx.CommitAsync();
            }
            catch
            {
                await tx.RollbackAsync();
                throw;
            }

            // Return the updated submission with the latest document (revision)
            return await GetSubmissionAsync(model.SubmissionId, formDocumentRevision.Id, true);
        }

        /// <inheritdoc/>
        public async Task<SubmissionDTO> ReopenSubmissionAsync(ReopenSubmissionDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            // Check for the submission entity.
            var submission = await _submissionsExcludingDeletedRepository.CheckSubmissionByIdAsync(model.SubmissionId);

            // Can only re-open when finalized
            if (submission.Status != SubmissionStatus.Submitted)
            {
                throw new ConstraintException(ApplicationErrors.SUBMISSION_INCORRECT_STATUS.ToErrorCode(), $"Submission has status '{submission.Status}");
            }

            // Get the last revision for the submission
            var formDocumentRevision = await _formDocumentRevisionsRepository.FindFirstOrDefaultByConditionAsync(fdr => fdr.FormDocumentId == submission.FormDocumentId,
                                                                                                                 q => q.Include(fdr => fdr.FormDocument)
                                                                                                                       .OrderByDescending(fdr => fdr.Revision));

            if (formDocumentRevision == null)
            {
                throw new NotFoundException(ApplicationErrors.FORMDOCUMENTREVISION_NOT_FOUND.ToErrorCode(), "No last revision found");
            }

            if (formDocumentRevision.Status != FormDocumentRevisionStatus.Finalized)
            {
                throw new ConstraintException(ApplicationErrors.FORMDOCUMENTREVISION_INCORRECT_STATUS.ToErrorCode(), $"Last revision has status '{formDocumentRevision.Status}");
            }

            if (formDocumentRevision.FormDocument.Status != FormDocumentStatus.Finalized)
            {
                throw new ConstraintException(ApplicationErrors.FORMDOCUMENT_INCORRECT_STATUS.ToErrorCode(), $"Document has status '{formDocumentRevision.FormDocument.Status}");
            }

            // Create a new revision based on the last revision
            var newFormDocumentRevision = new FormDocumentRevision
            {
                Status = FormDocumentRevisionStatus.Draft,
                DataAsJson = formDocumentRevision.DataAsJson,
                Revision = formDocumentRevision.Revision + 1,
            };

            formDocumentRevision.FormDocument.FormDocumentRevisions.Add(newFormDocumentRevision);

            // Set the status of the submission and the formdocument to 'revision'.
            formDocumentRevision.FormDocument.Status = FormDocumentStatus.Revision;
            submission.Status = SubmissionStatus.Revision;
            submission.ExportedAt = null; // Reset exported date when reopening

            // Store the comments as a SubmissionAttribute
            if (!string.IsNullOrEmpty(model.Comments))
            {
                submission.Attributes.SetAttributeValue(SubmissionAttributeKeys.ReopenRequestComments, model.Comments);

                await _systemAuditManager.AddActivityLogAsync(submission,
                                                           ActivityLogActivityTypes.SubmissionReopened,
                                                           "Submission reopened",
                                                           $"Submission '{submission.Name}' reopened with comments {model.Comments}");
            }
            else
            {
                await _systemAuditManager.AddActivityLogAsync(submission,
                                                              ActivityLogActivityTypes.SubmissionReopened,
                                                              "Submission reopened",
                                                              $"Submission '{submission.Name}' reopened");
            }

            await _formDocumentRevisionsRepository.SaveChangesAsync();

            // Return the updated submission with the newly created revision
            return await GetSubmissionAsync(model.SubmissionId, newFormDocumentRevision.Id, true);
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListSubmissionDTO>> ListSubmissionsAsync(ListSubmissionsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var submissions = await _submissionsExcludingDeletedRepository.FindByConditionAsPagedListMappedAsync<Submission, ListSubmissionDTO>(
                fd => fd.LegalEntityId == request.LegalEntityId &&
                      fd.ModuleId == request.ModuleId && fd.Status != SubmissionStatus.Temporal,
                _mapper.ConfigurationProvider,
                pageNumber: request.PagingInfo.PageNumber,
                pageSize: request.PagingInfo.PageSize,
                options: q => q
                              .TagWithCallSite()
                              .AsSplitQuery(),
                optionsMapped: q => ApplySorting(q, request.SortingInfo));

            return submissions;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListSubmissionDTO>> ListSubmissionsAsync(ListSubmissionsByMasterClientRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var submissions = await _submissionsExcludingDeletedRepository.FindByConditionAsPagedListMappedAsync<Submission, ListSubmissionDTO>(
                submissions => submissions.LegalEntity.MasterClientId == request.MasterClientId
                                && ((request.LegalEntityIds == null || !request.LegalEntityIds.Any()) || request.LegalEntityIds.Contains(submissions.LegalEntityId))
                                && (request.IsPaid == null || request.IsPaid == submissions.IsPaid)
                                && (request.HasInvoice == null || request.HasInvoice == submissions.InvoiceId.HasValue)
                                && ((request.FinancialYears == null || !request.FinancialYears.Any()) || request.FinancialYears.Contains(submissions.FinancialYear.Value))
                                && (request.SubmissionStatuses == null || !request.SubmissionStatuses.Any() || request.SubmissionStatuses.Contains(submissions.Status))
                                && submissions.Status != SubmissionStatus.Temporal,
                _mapper.ConfigurationProvider,
                pageNumber: request.PagingInfo.PageNumber,
                pageSize: request.PagingInfo.PageSize,
                options: q => q
                              .TagWithCallSite());

            return submissions;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListSubmissionDTO>> SearchSubmissionsAsync(SearchSubmissionsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            ArgumentNullException.ThrowIfNull(request.AuthorizedJurisdictionIDs, nameof(request.AuthorizedJurisdictionIDs));

            // Select the submissions
            var submissionPredicate = GetSubmissionPredicate(request);

            Expression<Func<ListSubmissionDTO, bool>> submissionDtoPredicate = s => true;
            submissionDtoPredicate = AddLegalEntitySearchTermPredicate(submissionDtoPredicate, request);

            Func<IRepository<Submission, Guid>, Task<IPagedList<ListSubmissionDTO>>> searchSubmissions =
                async (repository) =>
                {
                    return await repository.FindByConditionAsPagedListMappedAsync<Submission, ListSubmissionDTO>(
                        submissionPredicate,
                        _mapper.ConfigurationProvider,
                        pageNumber: request.PagingInfo.PageNumber,
                        pageSize: request.PagingInfo.PageSize,
                        options: q => q
                                    .TagWithCallSite()
                                    .AsSplitQuery(),
                        optionsMapped: q => ApplySorting(q.Where(submissionDtoPredicate), request.SortingInfo));
                };

            // Use the appropriate repository based on whether we want to include deleted submissions
            IRepository<Submission, Guid> repository = request.IsDeleted is not false ? _submissionsIncludingDeletedRepository : _submissionsExcludingDeletedRepository;
            var submissions = await searchSubmissions(repository);

            return submissions;
        }

        /// <inheritdoc/>
        public async Task<AvailableSubmissionYearsDTO> GetAvailableSubmissionYears(AvailableSubmissionYearsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            Check.NotDefaultOrNull<Guid>(request.LegalEntityId, nameof(request.LegalEntityId));
            Check.NotDefaultOrNull<Guid>(request.ModuleId, nameof(request.ModuleId));

            var result = new AvailableSubmissionYearsDTO
            {
                LegalEntityId = request.LegalEntityId,
                ModuleId = request.ModuleId
            };

            var years = new List<int>();
            var currentYear = DateTime.Today.Year;

            // Get entity
            var legalEntity = await _legalEntitiesRepository.CheckLegalEntityByIdAsync(request.LegalEntityId);

            // Get the list of submisisons for the legal entity and module
            var submissions = await _submissionsExcludingDeletedRepository.FindByConditionAsync(s =>
                s.LegalEntityId == request.LegalEntityId &&
                s.ModuleId == request.ModuleId);

            // Get the template for the module
            var template = await _formTemplatesRepository.FindFirstOrDefaultByConditionAsync(ft => ft.ModuleId == request.ModuleId,
                                                                                             q => q.Include(ft => ft.FormTemplateVersions));

            var minimumFirstYear = 2019;

            // Use the earliest PCP year if no incorporation date
            var firstYear = legalEntity.IncorporationDate.HasValue ? legalEntity.IncorporationDate.Value.Year : minimumFirstYear;
            if (firstYear < minimumFirstYear)
            {
                firstYear = minimumFirstYear;
            }

            // Get settings for submissions for the company
            var filingsettings = await _settingsManager.ReadSettingsForCompanyAsync<SubmissionSettingsDTO>(request.LegalEntityId);
            if (filingsettings.FirstSubmissionYear.HasValue && filingsettings.FirstSubmissionYear.Value > 0 && filingsettings.FirstSubmissionYear.Value > minimumFirstYear)
            {
                firstYear = filingsettings.FirstSubmissionYear.Value;
            }

            // Get the years we have templates for and that are within the range between first year and curent year
            foreach (var version in template.FormTemplateVersions.Where(ftv => ftv.Year.HasValue).OrderBy(ftv => ftv.Year))
            {
                if (version.Year.Value >= firstYear && version.Year.Value < currentYear)
                {
                    if (!years.Contains(version.Year.Value))
                    {
                        years.Add(version.Year.Value);
                    }
                }
            }

            // Check the submissions we already have and remove them from the list of years
            foreach (var submission in submissions.Where(s => s.FinancialYear.HasValue))
            {
                years.Remove(submission.FinancialYear.Value);
            }

            result.Years = years;
            return result;
        }

        /// <inheritdoc/>
        public async Task<AllSubmissionYearsDTO> GetAllSubmissionYears(AllSubmissionYearsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            Check.NotDefaultOrNull<Guid>(request.ModuleId, nameof(request.ModuleId));

            var result = new AllSubmissionYearsDTO
            {
                ModuleId = request.ModuleId
            };

            var years = new List<int>();
            var currentYear = DateTime.Today.Year;

            // Get the template for the module
            var template = await _formTemplatesRepository.FindFirstOrDefaultByConditionAsync(ft => ft.ModuleId == request.ModuleId,
                                                                                             q => q.Include(ft => ft.FormTemplateVersions));

            foreach (var version in template.FormTemplateVersions.Where(ftv => ftv.Year.HasValue).OrderBy(ftv => ftv.Year))
            {
                if (version.Year.Value < currentYear)
                {
                    if (!years.Contains(version.Year.Value))
                    {
                        years.Add(version.Year.Value);
                    }
                }
            }

            result.Years = years;
            return result;
        }

        /// <inheritdoc/>
        public async Task DeleteSubmissionAsync(Guid submissionId)
        {
            // Check the submission entity
            var submission = await _submissionsExcludingDeletedRepository.CheckSubmissionByIdAsync(
                submissionId,
                options: o => o.Include(s => s.LegalEntity));

            // Check the submission status
            if (submission.Status != SubmissionStatus.Draft && submission.Status != SubmissionStatus.Temporal)
            {
                throw new PreconditionFailedException(
                    ApplicationErrors.SUBMISSION_INCORRECT_STATUS.ToErrorCode(),
                    $"The submission with id {submissionId} cannot be deleted because the status is not Draft.");
            }

            // Mark the submission as deleted
            submission.Delete(_dateTimeProvider.UtcNow);

            // Log the deleted action
            await _systemAuditManager.AddActivityLogAsync(
                submission,
                ActivityLogActivityTypes.SubmissionDeleted,
                "Submission deleted",
                $"Submission '{submission.Name}' deleted",
                false);

            // Log the deleted action also on the legal entity
            await _systemAuditManager.AddActivityLogAsync(
                submission.LegalEntity,
                ActivityLogActivityTypes.SubmissionDeleted,
                "Submission deleted",
                $"Submission '{submission.Name}' deleted",
                false);

            await _submissionsIncludingDeletedRepository.UpdateAsync(submission, true);
        }

        /// <inheritdoc/>
        public async Task DeleteFormDocumentDocumentByDocumentIdAsync(Guid documentId, bool saveChanges = false)
        {
            // Check if the a form document document exists for the given document id.
            var formDocumentDocument = await _formDocumentDocumentsRepository.CheckFormDocumentDocumentByDocumentIdAsync(
                documentId,
                throwError: false);

            if (formDocumentDocument != null)
            {
                // Delete the formDocumentDocument if exist
                await _formDocumentDocumentsRepository.DeleteAsync(formDocumentDocument, saveChanges);
                await _documentManager.DeleteDocumentAsync(formDocumentDocument.DocumentId, saveChanges);
            }
        }

        /// <inheritdoc/>
        public async Task UpdateSubmissionGeneralInformationAsync(Guid submissionId, [NotNull] UpdateSubmissionInformationDTO data, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Check the submission entity
            var submission = await _submissionsExcludingDeletedRepository.CheckSubmissionByIdAsync(
                submissionId,
                options: q => q
                    .Include(s => s.LegalEntity.Jurisdiction));

            // Check for the jurisdiction to perform jurisdiction specific validations
            switch (submission.LegalEntity.Jurisdiction.Code)
            {
                case JurisdictionCodes.Bahamas:
                    await ValidateBahamasSubmissionAsync(submissionId, data, submission);
                    break;
                case JurisdictionCodes.Panama:
                    await ValidatePanamaSubmissionAsync(submissionId, data, submission);
                    break;
                default:
                    throw new ConstraintException(
                        ApplicationErrors.JURISDICTION_UNKNOWN.ToErrorCode(),
                        $"The jurisdiction '{submission.LegalEntity.Jurisdiction.Code}' is not configured.");
            }

            // Set the submission year
            submission.FinancialYear = data.EndAt.Year;

            // Set the submission date range
            submission.StartsAt = data.StartAt;
            submission.EndsAt = data.EndAt;

            // Set the status for the submission as Draft
            submission.Status = SubmissionStatus.Draft;

            await _submissionsExcludingDeletedRepository.UpdateAsync(submission, saveChanges);
        }

        /// <inheritdoc/>
        public async Task UpdateSubmissionGeneralInformationManagementAsync(Guid submissionId, [NotNull] UpdateSubmissionInformationDTO data, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Check the submission entity
            var submission = await _submissionsExcludingDeletedRepository.CheckSubmissionByIdAsync(
                submissionId,
                options: q => q
                    .Include(s => s.LegalEntity.Jurisdiction)
                    .Include(s => s.FormDocument.Attributes)
                    .Include(r => r.FormDocument.FormDocumentRevisions));

            // Check for the jurisdiction to perform jurisdiction specific validations
            switch (submission.LegalEntity.Jurisdiction.Code)
            {
                case JurisdictionCodes.Bahamas:
                    await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.ESBahamasModule_Set_Financial_Period);
                    await ValidateBahamasSubmissionAsync(submissionId, data, submission);
                    ValidateBahamasFinancialPeriod(submission);

                    await UpdateFinancialPeriodAsync(data, submission);

                    // Log the event
                    await _systemAuditManager.AddSubmissionFinancialPeriodChangedActivityLogAsync(submission, saveChanges);

                    await _submissionsExcludingDeletedRepository.UpdateAsync(submission, saveChanges);

                    break;
                default:
                    throw new ConstraintException(
                        ApplicationErrors.JURISDICTION_UNKNOWN.ToErrorCode(),
                        $"The jurisdiction '{submission.LegalEntity.Jurisdiction.Code}' is not configured.");
            }
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListSubmissionDTO>> SearchSubmissionsForPanamaAsync(FilterSubmissionsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            ArgumentNullException.ThrowIfNull(request.ModuleId, nameof(request.ModuleId));

            // Check for submissions that match the criteria
            var submissionPredicate = GetSubmissionPredicate(request);

            Func<IRepository<Submission, Guid>, Task<IPagedList<ListSubmissionDTO>>> searchSubmissions =
                async (repository) =>
                {
                    return await repository.FindByConditionAsPagedListMappedAsync<Submission, ListSubmissionDTO>(
                        submissionPredicate,
                        _mapper.ConfigurationProvider,
                        pageNumber: request.PageNumber,
                        pageSize: request.PageSize,
                        options: q => q.TagWithCallSite()
                                       .AsSplitQuery(),
                        optionsMapped: q => ApplySorting(q, request.ToSortingInfo()));
                };

            // Use the appropriate repository based on whether we want to include deleted submissions
            IRepository<Submission, Guid> repository = request.IsDeleted is not false ? _submissionsIncludingDeletedRepository : _submissionsExcludingDeletedRepository;
            var submissions = await searchSubmissions(repository);

            return submissions;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListSubmissionBahamasDTO>> SearchSubmissionsForBahamasAsync(FilterSubmissionsRequestForBahamas request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            ArgumentNullException.ThrowIfNull(request.ModuleId, nameof(request.ModuleId));

            // Check for submissions that match the criteria
            var submissionPredicate = GetSubmissionPredicate(request);

            // Retrieve the DTO predicate
            var submissionDtoPredicate = GetSubmissionDTOPredicate(request);

            Func<IRepository<Submission, Guid>, Task<IPagedList<ListSubmissionBahamasDTO>>> searchSubmissions =
                async (repository) =>
                {
                    return await repository.FindByConditionAsPagedListMappedAsync(
                        submissionPredicate,
                        _mapper.ConfigurationProvider,
                        pageNumber: request.PageNumber,
                        pageSize: request.PageSize,
                        options: q => q.TagWithCallSite()
                                       .AsSplitQuery(),
                        optionsMapped: q => ApplySorting(q, request.ToSortingInfo()),
                        projectedExpression: submissionDtoPredicate);
                };

            // Use the appropriate repository based on whether we want to include deleted submissions
            IRepository<Submission, Guid> repository = request.IsDeleted is not false ? _submissionsIncludingDeletedRepository : _submissionsExcludingDeletedRepository;
            var submissions = await searchSubmissions(repository);

            return submissions;
        }

        /// <inheritdoc/>
        public async Task<List<ListSubmissionBahamasDTO>> SearchSubmissionsForBahamasReportAsync(FilterSubmissionsRequestForBahamas request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            ArgumentNullException.ThrowIfNull(request.ModuleId, nameof(request.ModuleId));

            // Check for submissions that match the criteria
            var submissionPredicate = GetSubmissionPredicate(request);

            // Retrieve the DTO predicate
            var submissionDtoPredicate = GetSubmissionDTOPredicate(request);

            var submissions = await _submissionsIncludingDeletedRepository.FindByConditionAsPagedListMappedAsync(
                submissionPredicate,
                _mapper.ConfigurationProvider,
                options: q => q.TagWithCallSite()
                    .AsSplitQuery(),
                optionsMapped: q => ApplySorting(q, request.ToSortingInfo()),
                projectedExpression: submissionDtoPredicate);

            return submissions.ToList();
        }

        /// <inheritdoc/>
        public async Task<List<Submission>> FilterPanamaSubmissionsForReportAsync(FilterSubmissionsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            ArgumentNullException.ThrowIfNull(request.ModuleId, nameof(request.ModuleId));

            var submissionPredicate = GetSubmissionPredicate(request);

            // Use the appropriate repository based on whether we want to include deleted submissions
            var submissions = await _submissionsExcludingDeletedRepository.FindByConditionAsync(
                    submissionPredicate,
                    options: q => q
                        .Include(s => s.LegalEntity.MasterClient)
                        .Include(s => s.LegalEntity.Jurisdiction)
                        .Include(s => s.Invoice.PaymentInvoices).ThenInclude(pi => pi.Payment)
                        .Include(s => s.Attributes)
                        .Include(s => s.FormDocument.Attributes)
                        .TagWithCallSite()
                        .AsSplitQuery());

            return submissions.ToList();
        }

        /// <inheritdoc/>
        public async Task<List<Submission>> RetrieveSubmissionsByIdsAsync([JetBrains.Annotations.NotNull] RetrieveSubmissionsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            // Create the submission predicate
            Expression<Func<Submission, bool>> predicate = s => request.SubmissionIds.Contains(s.Id);
            predicate = predicate.And(_authorizationFilterExpressionFactory.GetSubmissionJurisdictionFilterPredicate(request));

            Func<IRepository<Submission, Guid>, Task<IEnumerable<Submission>>> searchSubmissions =
                async (repository) =>
                {
                    return await repository.FindByConditionAsync(predicate,
                        options: o => o.Include(s => s.LegalEntity.MasterClient)
                                       .Include(s => s.Invoice.PaymentInvoices).ThenInclude(pi => pi.Payment)
                                       .Include(s => s.Attributes)
                                       .Include(s => s.FormDocument.Attributes)
                                       .TagWithCallSite()
                                       .AsSplitQuery());
                };

            // Use the appropriate repository based on whether we want to include deleted submissions
            IRepository<Submission, Guid> repository = request.IncludeDeleted ? _submissionsIncludingDeletedRepository : _submissionsExcludingDeletedRepository;
            var submissions = await searchSubmissions(repository);

            return submissions.ToList();
        }

        /// <inheritdoc/>
        public async Task<List<SubmissionNevisReportDTO>> FilterNevisSubmissionsForReportAsync(SearchSubmissionsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            ArgumentNullException.ThrowIfNull(request.ModuleId, nameof(request.ModuleId));

            // Check for companies that match the criteria
            var submissionPredicate = GetSubmissionPredicate(request);
            submissionPredicate = AddLegalEntitySearchTermPredicate(submissionPredicate, request);

            var submissions = await _submissionsExcludingDeletedRepository.FindByConditionMappedAsync<Submission, SubmissionNevisReportDTO>(
                submissionPredicate,
                _mapper.ConfigurationProvider,
                options: q => q
                              .OrderBy(submission => submission.CreatedAt)
                              .TagWith(nameof(FilterNevisSubmissionsForReportAsync))
                              .AsSplitQuery());

            return submissions.ToList();
        }

        /// <inheritdoc/>
        public async Task<List<SubmissionDTO>> GetSubmissionsAsync(List<Guid> submissionIds)
        {
            Check.NotNull(submissionIds, nameof(submissionIds));

            var response = new List<SubmissionDTO>();

            foreach (var submissionId in submissionIds)
            {
                response.Add(await GetSubmissionAsync(submissionId, false, true));
            }

            return response;
        }

        /// <inheritdoc/>
        public async Task<List<Submission>> FilterBahamasSubmissionsForReportAsync(SearchSubmissionsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            ArgumentNullException.ThrowIfNull(request.ModuleId, nameof(request.ModuleId));

            // Check for companies that match the criteria
            var submissionPredicate = GetSubmissionPredicate(request);

            // Select the submissions
            var submissions = await _submissionsExcludingDeletedRepository.FindByConditionAsync(
                submissionPredicate,
                options: q => q
                              .Include(s => s.LegalEntity.MasterClient)
                              .Include(s => s.LegalEntity.Jurisdiction)
                              .Include(s => s.Invoice.PaymentInvoices).ThenInclude(pi => pi.Payment)
                              .Include(s => s.Attributes)
                              .Include(s => s.FormDocument.Attributes)
                              .TagWithCallSite()
                              .AsSplitQuery());

            return submissions.ToList();
        }

        /// <inheritdoc/>
        public async Task<string> GetModuleKeyForSubmissionAsync(Guid submissionId)
        {
            // Check the submission entity
            var submission = await _submissionsIncludingDeletedRepository.CheckSubmissionByIdAsync(
            submissionId,
            options: o => o.Include(s => s.Module));

            return submission.Module.Key;
        }

        private static Func<IQueryable<Submission>, IQueryable<Submission>> GetIncludesForMarkAsPaid()
        {
            return q => q.Include(s => s.LegalEntity.MasterClient)
                         .Include(s => s.LegalEntity.Jurisdiction)
                         .Include(s => s.LegalEntity.AnnualFees)
                         .Include(s => s.Invoice.Currency)
                         .Include(s => s.Invoice.PaymentInvoices)
                         .ThenInclude(pi => pi.Payment);
        }

        private static IQueryable<Submission> ApplyFilters(GetSubmissionListRequestDTO request, IQueryable<Submission> query)
        {
            if (request.CompanyId.HasValue)
            {
                query = query.Where(s => s.LegalEntityId == request.CompanyId.Value);
            }

            if (!string.IsNullOrEmpty(request.MasterClientCode))
            {
                query = query.Where(s => s.LegalEntity.MasterClient.Code == request.MasterClientCode);
            }

            query = query.Where(s => s.FinancialYear == request.FinancialYear);

            query = query.Where(s => s.IsPaid == true);
            query = query.Where(s => s.Invoice.PaymentInvoices
                .Any(pi => !pi.Payment.IsDeleted && pi.Payment.PaidAt.HasValue && pi.Payment.Status == Domain.Payments.PaymentStatus.Completed));

            return query;
        }

        /// <summary>
        /// Perform Bahamas specific validations before update the submission financial period and relevant activities.
        /// </summary>
        /// <param name="submission">The submission entity.</param>
        private static void ValidateBahamasFinancialPeriod(Submission submission)
        {
            // Get the Latest Form Revision
            var submissionFormDocument = submission.FormDocument;
            var revision = submissionFormDocument.FormDocumentRevisions
                                                 .OrderByDescending(rev => rev.Revision)
                                                 .First();

            // Deserializes the form data from JSON into a FormBuilder object, and casts the form to a KeyValueForm.
            var formBuilder = FormBuilder.FromJson(revision.DataAsJson);
            var form = formBuilder.Form as KeyValueForm;

            // Replace this line:
            foreach (var index in form.DataSet.GetIndexForRelevantActivities())
            {
                // Check if the relevant activity is selected
                var relevantActivitiesIsSelected = form.DataSet.GetValueOrDefault(FormKeys.RelevantActivitiesIsSelected(index));

                if (relevantActivitiesIsSelected == "true")
                {
                    // Check if any of the relevant activities is carried for only a part of the financial period
                    var isPartialFinancialPeriod = form.DataSet.GetValueOrDefault(FormKeys.RelevantActivitiesIsPartialFinancialPeriod(index));

                    if (isPartialFinancialPeriod == "true")
                    {
                        throw new PreconditionFailedException(
                        ApplicationErrors.INVALID_SUBMISSION_DATES.ToErrorCode(),
                        $"The relevant activity date is not equal to the submission date, therefore you cannot use this function, please use the re-open function instead.");
                    }

                    // If any of the start date of the relevant activities does not match the original submission start date
                    // An error is going to be thrown
                    var relevantActivitiesStartDate = form.DataSet.GetValueOrDefault(FormKeys.RelevantActivitiesStartDate(index));
                    if (DateTime.TryParse(relevantActivitiesStartDate, out var startDate))
                    {
                        if (startDate.ToUniversalTime().Date != submission.StartsAt.Value.Date)
                        {
                            throw new PreconditionFailedException(
                                ApplicationErrors.INVALID_SUBMISSION_DATES.ToErrorCode(),
                                $"The relevant activity date is not equal to the submission date, therefore you cannot use this function, please use the re-open function instead.");
                        }
                    }

                    // If any of the end date of the relevant activities does not match the original submission end date
                    // An error is going to be thrown
                    var relevantActivitiesEndDate = form.DataSet.GetValueOrDefault(FormKeys.RelevantActivitiesEndDate(index));
                    if (DateTime.TryParse(relevantActivitiesEndDate, out var endDate))
                    {
                        if (endDate.ToUniversalTime().Date != submission.EndsAt.Value.Date)
                        {
                            throw new PreconditionFailedException(
                            ApplicationErrors.INVALID_SUBMISSION_DATES.ToErrorCode(),
                            $"The relevant activity date is not equal to the submission date, therefore you cannot use this function, please use the re-open function instead.");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Updates the relevant activities' financial period fields in the form for the given submission.
        /// Sets the start and end date values for each relevant activity to match the submission's period.
        /// </summary>
        /// <param name="submission">The submission entity.</param>
        /// <param name="form">The form to update.</param>
        private static void UpdateRelevantActivitiesFinancialPeriodInFormAsync(Submission submission, KeyValueForm form)
        {
            // Filter the form dataset to find the relevant activities
            foreach (var index in form.DataSet.GetIndexForRelevantActivities())
            {
                // Check if the relevant activity is selected
                var relevantActivitiesIsSelected = form.DataSet.GetValueOrDefault(FormKeys.RelevantActivitiesIsSelected(index));

                // Check if any of the relevant activities is carried for only a part of the financial period
                var isPartialFinancialPeriod = form.DataSet.GetValueOrDefault(FormKeys.RelevantActivitiesIsPartialFinancialPeriod(index));

                // IsPartialFinancialPeriod is true, then we cannot update the relevantActivities financial period.
                if (relevantActivitiesIsSelected == "true" && isPartialFinancialPeriod != "true")
                {
                    // Get and update the relevant activity's financial period for start dates.
                    var relevantActivityStartDate = FormKeys.RelevantActivitiesStartDate(index);

                    if (form.DataSet.TryGetValue(relevantActivityStartDate, out var startDate))
                    {
                        form.SetFormValue(relevantActivityStartDate, submission.StartsAt.Value);
                    }

                    // Get and update the relevant activity's financial period for end dates.
                    var relevantActivityEndDate = FormKeys.RelevantActivitiesEndDate(index);

                    if (form.DataSet.TryGetValue(relevantActivityEndDate, out var endDate))
                    {
                        form.SetFormValue(relevantActivityEndDate, submission.EndsAt.Value);
                    }
                }
            }
        }

        /// <summary>
        /// Updates the financial period fields in the form for the given submission.
        /// </summary>
        /// <param name="submission">The submission entity.</param>
        /// <param name="form">The form to update.</param>
        private static void UpdateFinancialPeriodInFormAsync(Submission submission, KeyValueForm form)
        {
            // Update the financial period start date values
            form.SetFormValue(FormKeys.FinancialPeriodStartDate, submission.StartsAt.Value);

            // Update the financial period end date values.
            form.SetFormValue(FormKeys.FinancialPeriodEndDate, submission.EndsAt.Value);
        }

        /// <summary>
        /// Gets the predicate expression based on the provided filter criteria.
        /// </summary>
        /// <param name="filter">The filter criteria.</param>
        /// <returns>The predicate expression.</returns>
        private static Expression<Func<ListSubmissionBahamasDTO, bool>> GetSubmissionDTOPredicate(FilterSubmissionsRequestForBahamas filter)
        {
            ArgumentNullException.ThrowIfNull(filter, nameof(filter));

            // Setup base predicate
            Expression<Func<ListSubmissionBahamasDTO, bool>> predicate = s => s.Id != Guid.Empty;

            // Filter by the seledted relevant activities
            if (filter.RelevantActivities.Count > 0)
            {
                foreach (var relevantActivity in filter.RelevantActivities)
                {
                    switch (relevantActivity)
                    {
                        case WellKnownBahamasRelevantActivityKeys.None:
                            predicate = predicate.And(s => s.HasActivityNone == true);
                            break;
                        case WellKnownBahamasRelevantActivityKeys.HoldingBusiness:
                            predicate = predicate.And(s => s.HasActivityHoldingBusiness == true);
                            break;
                        case WellKnownBahamasRelevantActivityKeys.FinanceLeasingBusiness:
                            predicate = predicate.And(s => s.HasActivityFinanceLeasingBusiness == true);
                            break;
                        case WellKnownBahamasRelevantActivityKeys.BankingBusiness:
                            predicate = predicate.And(s => s.HasActivityBankingBusiness == true);
                            break;
                        case WellKnownBahamasRelevantActivityKeys.InsuranceBusiness:
                            predicate = predicate.And(s => s.HasActivityInsuranceBusiness == true);
                            break;
                        case WellKnownBahamasRelevantActivityKeys.FundManagementBusiness:
                            predicate = predicate.And(s => s.HasActivityFundManagementBusiness == true);
                            break;
                        case WellKnownBahamasRelevantActivityKeys.HeadquartersBusiness:
                            predicate = predicate.And(s => s.HasActivityHeadquartersBusiness == true);
                            break;
                        case WellKnownBahamasRelevantActivityKeys.ShippingBusiness:
                            predicate = predicate.And(s => s.HasActivityShippingBusiness == true);
                            break;
                        case WellKnownBahamasRelevantActivityKeys.IntellectualPropertyBusiness:
                            predicate = predicate.And(s => s.HasActivityIntellectualPropertyBusiness == true);
                            break;
                    }
                }
            }

            return predicate;
        }

        /// <summary>
        /// Gets the predicate expression based on the provided filter criteria.
        /// </summary>
        /// <param name="predicate">The existing predicate to add the criteria to.</param>
        /// <param name="filter">The filter criteria.</param>
        /// <returns>The predicate expression.</returns>
        private static Expression<Func<Submission, bool>> AddLegalEntitySearchTermPredicate(Expression<Func<Submission, bool>> predicate, FilterSubmissionsRequest filter)
        {
            ArgumentNullException.ThrowIfNull(filter, nameof(filter));

            if (!string.IsNullOrEmpty(filter.GeneralSearchTerm))
            {
                // Only one of the them needs to contain the search term
                var text = filter.GeneralSearchTerm;
                predicate = predicate.And(submission => submission.LegalEntity.Name.Contains(text) ||
                                                        submission.LegalEntity.MasterClient.Code.Contains(text) ||
                                                        submission.LegalEntity.ReferralOffice.Contains(text));
            }

            return predicate;
        }

        /// <summary>
        /// Gets the DTO (mapped) predicate expression based on the provided filter criteria.
        /// </summary>
        /// <param name="predicate">The existing predicate to add the criteria to.</param>
        /// <param name="filter">The filter criteria.</param>
        /// <returns>The predicate expression.</returns>
        private static Expression<Func<ListSubmissionDTO, bool>> AddLegalEntitySearchTermPredicate(Expression<Func<ListSubmissionDTO, bool>> predicate, SearchSubmissionsRequest filter)
        {
            ArgumentNullException.ThrowIfNull(filter, nameof(filter));

            if (!string.IsNullOrEmpty(filter.GeneralSearchTerm))
            {
                // Only one of them needs to contain the search term
                var text = filter.GeneralSearchTerm;
                predicate = predicate.And(submission => submission.LegalEntityName.Contains(text) ||
                                                        submission.MasterClientCode.Contains(text) ||
                                                        submission.LegalEntityReferralOffice.Contains(text) ||
                                                        submission.LegalEntityCode.Contains(text) ||
                                                        submission.LegalEntityVPCode.Contains(text) ||
                                                        submission.TxId.Contains(text));
            }
            else
            {
                // Use specific searches to 'AND' them
                if (!string.IsNullOrEmpty(filter.LegalEntitySearchTerm))
                {
                    var text = filter.LegalEntitySearchTerm;
                    predicate = predicate.And(submission => submission.LegalEntityName.Contains(text) ||
                                                            submission.LegalEntityCode.Contains(text) ||
                                                            submission.LegalEntityVPCode.Contains(text));
                }

                if (!string.IsNullOrEmpty(filter.ReferralOfficeSearchTerm))
                {
                    var text = filter.ReferralOfficeSearchTerm;
                    predicate = predicate.And(submission => submission.LegalEntityReferralOffice.Contains(text));
                }

                if (!string.IsNullOrEmpty(filter.MasterClientSearchTerm))
                {
                    var text = filter.MasterClientSearchTerm;
                    predicate = predicate.And(submission => submission.MasterClientCode.Contains(text));
                }
            }

            return predicate;
        }

        /// <summary>
        /// Gets the predicate expression based on the provided filter criteria.
        /// </summary>
        /// <param name="predicate">The existing predicate to add the criteria to.</param>
        /// <param name="filter">The filter criteria.</param>
        /// <returns>The predicate expression.</returns>
        private static Expression<Func<Submission, bool>> AddLegalEntityPredicate(Expression<Func<Submission, bool>> predicate, FilterSubmissionsRequestForBahamas filter)
        {
            ArgumentNullException.ThrowIfNull(filter, nameof(filter));

            if (!string.IsNullOrEmpty(filter.GeneralSearchTerm))
            {
                // Only one of the them needs to contain the search term
                var text = filter.GeneralSearchTerm;
                predicate = predicate.And(submission => submission.LegalEntity.Name.Contains(text) ||
                                                        submission.LegalEntity.MasterClientCode.Contains(text) ||
                                                        submission.LegalEntity.ReferralOffice.Contains(text) ||
                                                        submission.LegalEntity.LegacyCode.Contains(text) ||
                                                        submission.LegalEntity.Code.Contains(text));
            }

            // Filter by the incorporation date
            if (filter.CompanyIncorporatedAfterDate.HasValue)
            {
                // Only one of the them needs to contain the search term
                predicate = predicate.And(submission => submission.LegalEntity.IncorporationDate >= filter.CompanyIncorporatedAfterDate.Value);
            }

            if (filter.CompanyIncorporatedBeforeDate.HasValue)
            {
                // Only one of the them needs to contain the search term
                predicate = predicate.And(submission => submission.LegalEntity.IncorporationDate <= filter.CompanyIncorporatedBeforeDate.Value);
            }

            return predicate;
        }

        /// <summary>
        /// Gets the predicate expression based on the provided filter criteria.
        /// </summary>
        /// <param name="predicate">The existing predicate to add the criteria to.</param>
        /// <param name="filter">The filter criteria.</param>
        /// <returns>The predicate expression.</returns>
        private static Expression<Func<Submission, bool>> AddLegalEntitySearchTermPredicate(Expression<Func<Submission, bool>> predicate, SearchSubmissionsRequest filter)
        {
            ArgumentNullException.ThrowIfNull(filter, nameof(filter));

            if (!string.IsNullOrEmpty(filter.GeneralSearchTerm))
            {
                // Only one of them needs to contain the search term
                var text = filter.GeneralSearchTerm;
                predicate = predicate.And(submission => submission.LegalEntity.Name.Contains(text) ||
                                                        submission.LegalEntity.MasterClient.Code.Contains(text) ||
                                                        submission.LegalEntity.ReferralOffice.Contains(text) ||
                                                        submission.LegalEntity.Code.Contains(text) ||
                                                        submission.LegalEntity.LegacyCode.Contains(text));
            }
            else
            {
                // Use specific searches to 'AND' them
                if (!string.IsNullOrEmpty(filter.LegalEntitySearchTerm))
                {
                    var text = filter.LegalEntitySearchTerm;
                    predicate = predicate.And(submission => submission.LegalEntity.Name.Contains(text) ||
                                                            submission.LegalEntity.Code.Contains(text) ||
                                                            submission.LegalEntity.LegacyCode.Contains(text));
                }

                if (!string.IsNullOrEmpty(filter.ReferralOfficeSearchTerm))
                {
                    var text = filter.ReferralOfficeSearchTerm;
                    predicate = predicate.And(submission => submission.LegalEntity.ReferralOffice.Contains(text));
                }

                if (!string.IsNullOrEmpty(filter.MasterClientSearchTerm))
                {
                    var text = filter.MasterClientSearchTerm;
                    predicate = predicate.And(submission => submission.LegalEntity.MasterClient.Code.Contains(text));
                }
            }

            return predicate;
        }

        private static IQueryable<ListSubmissionDTO> ApplySorting(IQueryable<ListSubmissionDTO> query, SortingInfo sortingInfo)
        {
            if (sortingInfo == null)
            {
                return query;
            }

            sortingInfo = sortingInfo.Validate();

            var sortingColumns = new Dictionary<string, Expression<Func<ListSubmissionDTO, object>>>()
            {
                {
                    nameof(ListSubmissionDTO.Status), s =>
                    s.Status == SubmissionStatus.Draft ? 1 :
                    s.Status == SubmissionStatus.Paid ? 2 :
                    s.Status == SubmissionStatus.Submitted ? 3 :
                    4
                }
            };
            Expression<Func<ListSubmissionDTO, object>> defaultSort = s => s.CreatedAt;

            return query.SortBySpecification<ListSubmissionDTO, SearchSubmissionsRequestDTO>(sortingInfo, defaultSort, sortingColumns);
        }

        private static IQueryable<ListSubmissionBahamasDTO> ApplySorting(IQueryable<ListSubmissionBahamasDTO> query, SortingInfo sortingInfo)
        {
            if (sortingInfo == null)
            {
                return query;
            }

            sortingInfo = sortingInfo.Validate();

            var sortingColumns = new Dictionary<string, Expression<Func<ListSubmissionBahamasDTO, object>>>()
            {
                { nameof(ListSubmissionBahamasDTO.LegalEntityName), s => s.LegalEntityName },
                { nameof(ListSubmissionBahamasDTO.LegalEntityCode), s => s.LegalEntityCode },
                { nameof(ListSubmissionBahamasDTO.LegalEntityVPCode), s => s.LegalEntityName },
                { nameof(ListSubmissionBahamasDTO.LegalEntityVPStatus), s => s.LegalEntityVPStatus },
                { nameof(ListSubmissionBahamasDTO.MasterClientCode), s => s.MasterClientCode },
                {
                    nameof(ListSubmissionBahamasDTO.Status), s =>
                    s.Status == SubmissionStatus.Draft ? 1 :
                    s.Status == SubmissionStatus.Paid ? 2 :
                    s.Status == SubmissionStatus.Submitted ? 3 :
                    4
                },
                { nameof(ListSubmissionBahamasDTO.CreatedAt), s => s.CreatedAt },
                { nameof(ListSubmissionBahamasDTO.ExportedAt), s => s.ExportedAt },
                { nameof(ListSubmissionBahamasDTO.SubmittedAt), s => s.SubmittedAt },
                { nameof(ListSubmissionBahamasDTO.PaymentMethod), s => s.PaymentMethod },
                { nameof(ListSubmissionBahamasDTO.PaymentReceivedAt), s => s.PaymentReceivedAt },
                { nameof(ListSubmissionBahamasDTO.FinancialPeriodStartsAt), s => s.FinancialPeriodStartsAt },
                { nameof(ListSubmissionBahamasDTO.FinancialPeriodEndsAt), s => s.FinancialPeriodEndsAt },
                { nameof(SubmissionDTO.PaymentReference), s => s.PaymentReference }
            };
            Expression<Func<ListSubmissionBahamasDTO, object>> defaultSort = s => s.CreatedAt;

            return query.Sort(sortingInfo, sortingColumns, defaultSort);
        }

        private async Task MarkSubmissionAsPaid(bool isPaid, Submission submission)
        {
            if (submission.IsPaid == isPaid)
            {
                _logger.LogInformation("Submission '{SubmissionId}' already marked as {IsPaid}", submission.Id, isPaid ? "paid" : "unpaid");
                return;
            }

            var submissionId = submission.Id;
            if (submission.Status == SubmissionStatus.Draft)
            {
                _logger.LogError("Submission '{SubmissionId}' has status 'Draft'", submissionId);
                throw new PreconditionFailedException(ApplicationErrors.SUBMISSION_INCORRECT_STATUS.ToErrorCode(), "Submission has status 'Draft'");
            }

            var invoice = submission.Invoice;
            if (invoice == null)
            {
                if (await SubmissionNeedsInvoice(submission))
                {
                    _logger.LogError("Submission '{SubmissionId}' has no invoice", submissionId);
                    throw new PreconditionFailedException(ApplicationErrors.SUBMISSION_NO_INVOICE.ToErrorCode(), "Submission has no invoice");
                }
            }
            else
            {
                await _invoicesManager.MarkInvoiceAsPaidAsync(invoice, isPaid, false);
            }

            _logger.LogInformation("Marking submission '{SubmissionId}' as {IsPaid}", submissionId, isPaid ? "paid" : "unpaid");
            submission.SetPaid(isPaid, _dateTimeProvider.UtcNow);

            await _systemAuditManager.AddActivityLogAsync(submission,
                ActivityLogActivityTypes.SubmissionPaidStatusUpdated,
                $"Submission marked as {(isPaid ? "paid" : "unpaid")}",
                $"Submission '{submission.Name}' payment status updated to {(isPaid ? "paid" : "unpaid")}");

            // Set the company's AnnualFee paid status if applicable
            if (isPaid)
            {
                var legalEntity = submission.LegalEntity;
                var allYears = legalEntity.Jurisdiction.GetAnnualFeeYears();
                var submissionFinancialYear = submission.FinancialYear!.Value;
                if (allYears.Contains(submissionFinancialYear))
                {
                    var annualFeeStatus = legalEntity.UpdateAnnualFeeStatus(submissionFinancialYear, true);

                    await _systemAuditManager.AddAnnualFeeStatusUpdatedActivityLogAsync(annualFeeStatus, legalEntity, saveChanges: false);
                }
            }
        }

        private async Task<bool> SubmissionNeedsInvoice(Submission submission)
        {
            var module = await _modulesRepository.GetByIdAsync(submission.ModuleId.Value);
            switch (module.Key)
            {
                case ModuleKeyConsts.SimplifiedTaxReturn:
                    if (submission.FinancialYear >= 2024)
                    {
                        _logger.LogInformation("Not creating an invoice for {Module} because financial year ({Year}) is 2024 or later", module.Name, submission.FinancialYear);
                        return false;
                    }

                    break;
                case ModuleKeyConsts.EconomicSubstanceBahamas:
                case ModuleKeyConsts.BasicFinancialReportPanama:
                    return false;
                default:
                    throw new NotImplementedException($"Module {module.Key} not implemented");
            }

            return true;
        }

        private async Task<SubmissionDTO> GetSubmissionAsync(Guid submissionId, Guid submissionRevisionId, bool includeFormDocument)
        {
            Check.NotDefaultOrNull<Guid>(submissionId, nameof(submissionId));

            // Check for the submission entity.
            var submission = await _submissionsIncludingDeletedRepository.CheckSubmissionByIdAsync(submissionId,
                                                                                   options: (q) => q.Include(s => s.LegalEntity).ThenInclude(le => le.Jurisdiction));

            FormDocumentRevision formDocumentRevision = null;
            if (includeFormDocument)
            {
                // Get the given revision for the submission
                formDocumentRevision = await _formDocumentRevisionsRepository.FindFirstOrDefaultByConditionAsync(fdr => fdr.Id == submissionRevisionId,
                                                                                                                 q => q.Include(fdr => fdr.FormDocument));
            }

            var result = _mapper.Map<SubmissionDTO>(submission);
            result.FormDocument = _mapper.Map<FormDocumentWithRevisionsDTO>(formDocumentRevision.FormDocument);

            // In case there are multiple revisions (after reopening)
            if (result.FormDocument.Revisions.Count > 1)
            {
                result.FormDocument.Revisions = new FormDocumentRevisionDTO[] { result.FormDocument.Revisions.FirstOrDefault(r => r.Id == submissionRevisionId) };
            }

            return result;
        }

        /// <summary>
        /// Perform Panama specific validations before update the submission information.
        /// </summary>
        /// <param name="submissionId">The submisson id as Guid.</param>
        /// <param name="data">The data necessary to update the submission general information.</param>
        /// <param name="submission">The submission entity.</param>
        private async Task ValidatePanamaSubmissionAsync(Guid submissionId, UpdateSubmissionInformationDTO data, Submission submission)
        {
            // Check if the jurisdiction is Panama
            if (submission.LegalEntity.Jurisdiction.Code != JurisdictionCodes.Panama)
            {
                throw new PreconditionFailedException(
                    ApplicationErrors.INVALID_JURISDICTION.ToErrorCode(),
                    $"The jurisdiction for the submission is not supported.");
            }

            // Checks and validations
            Check.NotDefaultOrNull<DateTime>(data.StartAt, nameof(data.StartAt));
            Check.NotDefaultOrNull<DateTime>(data.EndAt, nameof(data.EndAt));

            // Check the submission date range
            if (data.StartAt > data.EndAt)
            {
                throw new BadRequestException(
                    ApplicationErrors.INVALID_SUBMISSION_DATE.ToErrorCode(),
                    $"The start at date cannot be later than the ends at date.");
            }

            if (data.EndAt.Date >= data.StartAt.Date.AddMonths(12))
            {
                throw new BadRequestException(
                    ApplicationErrors.INVALID_SUBMISSION_DATE.ToErrorCode(),
                    $"The end date cannot be more than 12 months greater than the start date.");
            }

            // Avoid overlapping periods between submissions in the same company and module.
            var overlappingSubmissions = await _submissionsExcludingDeletedRepository.FindByConditionAsync(
                s => (s.StartsAt < data.EndAt && s.EndsAt > data.StartAt) &&
                     s.Id != submissionId &&
                     s.ModuleId == submission.ModuleId && s.LegalEntityId == submission.LegalEntityId);

            if (overlappingSubmissions.Any())
            {
                throw new BadRequestException(
                    ApplicationErrors.INVALID_SUBMISSION_DATES.ToErrorCode(),
                    $"The date range selected overlaps with existing submissions.");
            }
        }

        /// <summary>
        /// Perform Bahamas specific validations before update the submission information.
        /// </summary>
        /// <param name="submissionId">The submisson id as Guid.</param>
        /// <param name="data">The data necessary to update the submission general information.</param>
        /// <param name="submission">The submission entity.</param>
        private async Task ValidateBahamasSubmissionAsync(Guid submissionId, UpdateSubmissionInformationDTO data, Submission submission)
        {
            // Check if the jurisdiction is Panama
            if (submission.LegalEntity.Jurisdiction.Code != JurisdictionCodes.Bahamas)
            {
                throw new PreconditionFailedException(
                    ApplicationErrors.INVALID_JURISDICTION.ToErrorCode(),
                    $"The jurisdiction for the submission is not supported.");
            }

            // Checks and validations
            Check.NotDefaultOrNull<DateTime>(data.StartAt, nameof(data.StartAt));
            Check.NotDefaultOrNull<DateTime>(data.EndAt, nameof(data.EndAt));

            // Check the submission date range
            if (data.StartAt > data.EndAt)
            {
                throw new BadRequestException(
                    ApplicationErrors.INVALID_SUBMISSION_DATE.ToErrorCode(),
                    $"The start at date cannot be later than the ends at date.");
            }

            if (data.EndAt.Date >= data.StartAt.Date.AddMonths(12))
            {
                throw new BadRequestException(
                    ApplicationErrors.INVALID_SUBMISSION_DATE.ToErrorCode(),
                    $"The end date cannot be more than 12 months greater than the start date.");
            }

            if (data.EndAt >= DateTime.UtcNow)
            {
                throw new BadRequestException(
                    ApplicationErrors.INVALID_SUBMISSION_DATE.ToErrorCode(),
                    $"The end date cannot be greater than the current date.");
            }

            // Validation for Bahamas Start Date is only editable if this is the first filing of the company
            var lastSubmission = await _submissionsExcludingDeletedRepository.FindByConditionAsync(
                s => s.Id != submissionId &&
                     s.ModuleId == submission.ModuleId && s.LegalEntityId == submission.LegalEntityId);

            if (lastSubmission.Any())
            {
                var lastSubmissionDate = lastSubmission.OrderByDescending(s => s.EndsAt).FirstOrDefault()?.EndsAt;

                if (lastSubmissionDate is not null && ((DateTime)lastSubmissionDate).AddDays(1) != (DateTime)data.StartAt)
                {
                    throw new PreconditionFailedException(
                        ApplicationErrors.INVALID_SUBMISSION_DATES.ToErrorCode(),
                        $"The start date is only editable if it is the first filing of the company.");
                }
            }
        }

        /// <summary>
        /// Updates the relevant activities financial periods for Bahamas.
        /// </summary>
        /// <param name="data">Update Submission Information.</param>
        /// <param name="submission">The submission entity.</param>
        private async Task UpdateFinancialPeriodAsync(UpdateSubmissionInformationDTO data, Submission submission)
        {
            // Set the submission year
            submission.FinancialYear = data.EndAt.Year;

            // Set the submission date range
            submission.StartsAt = data.StartAt;
            submission.EndsAt = data.EndAt;

            // Get the Latest Form Revision
            var submissionFormDocument = submission.FormDocument;
            var revision = submissionFormDocument.FormDocumentRevisions
                                                 .OrderByDescending(rev => rev.Revision)
                                                 .First();

            // Deserializes the form data from JSON into a FormBuilder object, and casts the form to a KeyValueForm.
            var formBuilder = FormBuilder.FromJson(revision.DataAsJson);
            var form = formBuilder.Form as KeyValueForm;

            // Updates the form’s internal fields for the financial period
            UpdateFinancialPeriodInFormAsync(submission, form);

            // Updates the form’s internal fields for the relevant activities financial period
            UpdateRelevantActivitiesFinancialPeriodInFormAsync(submission, form);

            // Sync the form’s key-value data with the form document’s attributes in the database.
            await UpdateFormAttributes(form, submissionFormDocument);

            // Serializes the updated form back to JSON and stores it in the latest revision.
            var json = formBuilder.ToJson();
            revision.DataAsJson = json;

            await _submissionsExcludingDeletedRepository.UpdateAsync(submission, true);
        }

        /// <summary>
        /// Starts a submission for the Panama jurisdiction.
        /// </summary>
        /// <param name="model">The necessary data used to start a submission as StartSubmissionDTO.</param>
        /// <param name="jurisdiction">The jurisdiction entity.</param>
        /// <param name="module">The module entity.</param>
        /// <param name="userEmail">The email of the user starting the submission.</param>
        /// <param name="userId">The ID of the user starting the submission.</param>
        /// <returns>The created submission as SubmissionDTO.</returns>
        private async Task<SubmissionDTO> StartSubmissionForNevisAsync(StartSubmissionDTO model, Jurisdiction jurisdiction, Module module, string userEmail, Guid userId)
        {
            // Checks and validations
            Check.NotDefaultOrNull(model.FinancialYear, nameof(model.FinancialYear));

            // Check if we already have a submission for this year
            var exists = await _submissionsExcludingDeletedRepository.AnyByConditionAsync(
                s => s.LegalEntityId == model.LegalEntityId &&
                    s.ModuleId == model.ModuleId &&
                    s.FinancialYear == model.FinancialYear);
            if (exists)
            {
                throw new ConstraintException(ApplicationErrors.SUBMISSION_FOR_YEAR_EXISTS.ToErrorCode(), $"A submission for this company/module already exists for year {model.FinancialYear}");
            }

            // Get the template
            var formTemplate = await _formTemplatesRepository.FindByConditionAsync(ft => ft.JurisdictionId == jurisdiction.Id && ft.ModuleId == model.ModuleId,
                                                                                   q => q.Include(ft => ft.FormTemplateVersions).Include(ft => ft.Module));

            // Sanity
            switch (formTemplate.Count())
            {
                case 0:
                    {
                        throw new BadRequestException($"No templates found for submission for module '{module.Name}' in jurisdiction '{jurisdiction.Name}'");
                    }

                case 1: break;
                default:
                    {
                        throw new ConstraintException($"Multiple templates found for submission for module '{module.Name}' in jurisdiction '{jurisdiction.Name}'");
                    }
            }

            // Get the version for the given year
            var versions = formTemplate.First().FormTemplateVersions.Where(ftv => ftv.Year == model.FinancialYear).ToArray();

            // Sanity
            switch (versions.Length)
            {
                case 0:
                    {
                        throw new ConstraintException($"No version found for submission for module '{module.Name}' in jurisdiction '{jurisdiction.Name}' in template '{formTemplate.First().Name}' for year {model.FinancialYear}");
                    }

                case 1: break;
                default:
                    {
                        throw new ConstraintException($"Multiple versions found for submission for module '{module.Name}' in jurisdiction '{jurisdiction.Name}' in template '{formTemplate.First().Name}' for year {model.FinancialYear}");
                    }
            }

            var templateVersion = versions.First();

            return await StartSubmission(model, templateVersion, userEmail, userId);
        }

        /// <summary>
        /// Starts a submission for the Panama jurisdiction.
        /// </summary>
        /// <param name="model">The necessary data used to start a submission as StartSubmissionDTO.</param>
        /// <param name="jurisdiction">The jurisdiction entity.</param>
        /// <param name="module">The module entity.</param>
        /// <param name="userEmail">The email of the user starting the submission.</param>
        /// <param name="userId">The ID of the user starting the submission.</param>
        /// <returns>The created submission as SubmissionDTO.</returns>
        private async Task<SubmissionDTO> StartSubmissionAsync(StartSubmissionDTO model, Jurisdiction jurisdiction, Module module, string userEmail, Guid userId)
        {
            // Check if there is an active submission for the given company and module
            var exists = await _submissionsExcludingDeletedRepository.AnyByConditionAsync(
                s => s.LegalEntityId == model.LegalEntityId &&
                    s.ModuleId == model.ModuleId &&
                    (s.Status != SubmissionStatus.Submitted && s.Status != SubmissionStatus.Temporal));

            if (exists)
            {
                throw new ConstraintException(ApplicationErrors.SUBMISSION_ALREADY_EXISTS.ToErrorCode(), $"An active submission for this company/module already exists.");
            }

            // Get the template
            var formTemplate = await _formTemplatesRepository.FindByConditionAsync(
                ft => ft.JurisdictionId == jurisdiction.Id &&
                    ft.ModuleId == model.ModuleId,
                q => q.Include(ft => ft.FormTemplateVersions).Include(ft => ft.Module));

            // Sanity
            switch (formTemplate.Count())
            {
                case 0:
                    {
                        throw new BadRequestException($"No templates found for submission for module '{module.Name}' in jurisdiction '{jurisdiction.Name}'");
                    }

                case 1: break;
                default:
                    {
                        throw new ConstraintException($"Multiple templates found for submission for module '{module.Name}' in jurisdiction '{jurisdiction.Name}'");
                    }
            }

            // Get the default version for the formTemplate
            var templateVersion = formTemplate.First().FormTemplateVersions.First();

            if (templateVersion == null)
            {
                throw new ConstraintException($"No version found for submission for module '{module.Name}' in jurisdiction '{jurisdiction.Name}' in template '{formTemplate.First().Name}'.");
            }

            // Check if there is a temporal submission to avoid creating a new one.
            var temporalSubmission = await _submissionsExcludingDeletedRepository.FindFirstOrDefaultByConditionAsync(
                s => s.Status == SubmissionStatus.Temporal &&
                    s.LegalEntityId == model.LegalEntityId &&
                    s.ModuleId == model.ModuleId);

            if (temporalSubmission != null)
            {
                var submissionData = await GetSubmissionAsync(temporalSubmission.Id, true);
                return _mapper.Map<SubmissionDTO>(submissionData);
            }

            return await StartSubmission(model, templateVersion, userEmail, userId, SubmissionStatus.Temporal);
        }

        /// <summary>
        /// Starts a submission for the given template version and data.
        /// </summary>
        /// <param name="model">The necessary data to create a submission.</param>
        /// <param name="templateVersion">The version to use as FormTemplateVersion.</param>
        /// <param name="userEmail">The email of the user starting the submission.</param>
        /// <param name="userId">The ID of the user starting the submission.</param>
        /// <param name="status">The desired status for the submission.</param>
        /// <returns>The created submission as SubmissionDTO.</returns>
        private async Task<SubmissionDTO> StartSubmission(StartSubmissionDTO model, FormTemplateVersion templateVersion, string userEmail, Guid userId, SubmissionStatus status = SubmissionStatus.Draft)
        {
            // Create a FormDocument from this template and set status to 'draft'
            var formDocument = new FormDocument(Guid.NewGuid())
            {
                FormTemplateVersionId = templateVersion.Id,
                LegalEntityId = model.LegalEntityId,
                ModuleId = model.ModuleId,
                Name = templateVersion.Name,
                Year = model.FinancialYear,
                Status = FormDocumentStatus.Draft
            };

            var form = FormBuilder.FromJson(templateVersion.DataAsJson).Form as KeyValueForm;
            if (form == null)
            {
                throw new ArgumentException("The template form data is not valid or cannot be parsed as a KeyValueForm.", nameof(templateVersion));
            }

            // Create a new revision and add it to the document
            var formDocumentRevision = new FormDocumentRevision
            {
                Revision = 0,
                Status = FormDocumentRevisionStatus.Draft,
                DataAsJson = templateVersion.DataAsJson,
            };

            // if Bahamas Set previousSubmissionEndDate
            var module = await _modulesRepository.CheckModuleByIdAsync(model.ModuleId);

            if (module.Key == ModuleKeyConsts.EconomicSubstanceBahamas)
            {
                var lastSubmission = await _submissionsExcludingDeletedRepository.FindFirstOrDefaultByConditionAsync(s => s.ModuleId == model.ModuleId && s.LegalEntityId == model.LegalEntityId, options: s => s.OrderByDescending(o => o.EndsAt));
                if (lastSubmission != null)
                {
                    form.SetFormValue("financial-period.previousSubmissionEndDate", lastSubmission.EndsAt);
                }

                formDocumentRevision.DataAsJson = form.ToJson();
            }

            formDocument.FormDocumentRevisions.Add(formDocumentRevision);

            // Create a submission and assign the FormDocument
            var id = Guid.NewGuid();
            var submission = new Submission(id)
            {
                Name = templateVersion.Name,
                FinancialYear = model.FinancialYear,
                ReportId = id.ToString(),
                FormDocument = formDocument,
                LegalEntityId = model.LegalEntityId,
                ModuleId = model.ModuleId,
                Status = status,
                Layout = LayoutConsts.TridentTrust,
                CreatedBy = userId,
            };

            submission.Attributes.SetAttributeValue(SubmissionAttributeKeys.CreatedByEmail, userEmail);

            await _submissionsExcludingDeletedRepository.InsertAsync(submission);

            await _systemAuditManager.AddActivityLogAsync(submission,
                                                          ActivityLogActivityTypes.SubmissionStarted,
                                                          "Submission started",
                                                          $"Submission '{submission.Name}' started");

            await _submissionsExcludingDeletedRepository.SaveChangesAsync();

            var result = await GetSubmissionAsync(id);

            return result;
        }

        /// <summary>
        /// Creates or updates an submissionDocument to the application.
        /// </summary>
        /// <param name="formDocumentId">The submission Id as Guid.</param>
        /// <param name="documentIds">A list of DocumentId values as Guid.</param>
        /// <returns>The created submission document id as <see cref="Guid"/>.</returns>
        private async Task CreateUpdateFormDocumentDocumentsAsync(Guid formDocumentId, List<Guid> documentIds)
        {
            // Check the form document entity
            var formDocument = await _formDocumentsRepository.CheckFormDocumentByIdAsync(formDocumentId);

            foreach (var documentId in documentIds)
            {
                // Check the document entity
                var document = await _documentManager.CheckDocumentByIdAsync(documentId);

                // Check if there is alredy a formDocumentDocument entity for the given FormDocumentId and DocumentId
                var formDocumentDocumentCheck = await _formDocumentDocumentsRepository.FindFirstOrDefaultByConditionAsync(
                    fdd => fdd.FormDocumentId == formDocumentId && fdd.DocumentId == documentId);

                if (formDocumentDocumentCheck == null)
                {
                    // Create a FormDcumentDocument entity
                    var formDocumentDocument = new FormDocumentDocument()
                    {
                        DocumentId = document.Id,
                        FormDocumentId = formDocument.Id
                    };

                    await _formDocumentDocumentsRepository.InsertAsync(formDocumentDocument, false);

                    // Set the expiration date as null for the document
                    await _documentManager.ResetDocumentExpirationDateAsync(documentId);
                }
            }

            // Delete removed formDocumentDocuments
            var deletedFormDocumentDocuments = await _formDocumentDocumentsRepository.FindByConditionAsync(
                fdd => !documentIds.Contains(fdd.DocumentId) && fdd.FormDocumentId == formDocumentId);

            foreach (var formDocumentDocument in deletedFormDocumentDocuments)
            {
                await DeleteFormDocumentDocumentByDocumentIdAsync(formDocumentDocument.DocumentId, false);
            }
        }

        /// <summary>
        /// Creates a new invoice for the given submission when this is for an STR submission.
        /// </summary>
        /// <param name="submission">The submission to create an invoice for.</param>
        /// <returns>The created invoice.</returns>
        private async Task<Invoice> CreateInvoiceForSTRSubmissionAsync(Submission submission)
        {
            var legalEntity = await _legalEntitiesRepository.FindFirstOrDefaultByConditionAsync(
                le => le.Id == submission.LegalEntityId,
                q => q.Include(le => le.Jurisdiction)
                      .Include(le => le.AnnualFees));

            if (legalEntity.AnnualFees.Any(af => af.FinancialYear == submission.FinancialYear && af.IsPaid))
            {
                // The annual fee is already paid, so we don't need to create an invoice
                // and we can mark the submission as paid.
                _logger.LogInformation("Not creating an invoice and setting IsPaid to true because the annual fee is already paid for year {Year}", submission.FinancialYear);
                submission.SetPaid(true, _dateTimeProvider.UtcNow);

                await _systemAuditManager.AddActivityLogAsync(submission,
                    ActivityLogActivityTypes.SubmissionPaidStatusUpdated,
                    $"Submission marked as paid because annual fee was already paid",
                    $"Submission '{submission.Name}' payment status updated to paid because annual fee was already paid", saveChanges: false);

                return null;
            }

            // Get the financial settings for the jurisdiction
            var financialSettings = await _settingsManager.ReadSettingsForJurisdictionAsync<FinancialSettingsDTO>(legalEntity.JurisdictionId!.Value);
            if (string.IsNullOrEmpty(financialSettings.CurrencyCode))
            {
                financialSettings.CurrencyCode = "USD";
            }

            // Get the settings for the fee's and derive values from masterclient or jurisdicition if none given for the company
            var feeSettings = await _settingsManager.GetDerivedSettingsForCompany<FeeSettingsDTO>(submission.LegalEntityId);
            if (feeSettings.STRSubmissionFee.GetValueOrDefault(0) <= 0)
            {
                _logger.LogInformation("Not creating an invoice because fee amount is set to {Fee}", feeSettings.STRSubmissionFee);
                return null;
            }

            // No longer create an invoice for Nevis STR when year is 2024 or later
            if (!await SubmissionNeedsInvoice(submission))
            {
                _logger.LogInformation("Not creating an invoice for submission because it is not needed");
                return null;
            }

            var invoiceRequest = new CreateInvoiceRequest
            {
                CurrencyCode = financialSettings.CurrencyCode,
                LegalEntityId = submission.LegalEntityId,
                FinancialYear = submission.FinancialYear
            };

            var invoiceLines = new List<CreateInvoiceLine>();

            // Create an invoice line for the regular fee
            var invoiceText = feeSettings.STRSubmissionFeeInvoiceText;
            invoiceText = invoiceText.Replace("{year}", submission.FinancialYear.ToString(), StringComparison.OrdinalIgnoreCase);

            invoiceLines.Add(new CreateInvoiceLine
            {
                Sequence = invoiceLines.Count + 1,
                Amount = feeSettings.STRSubmissionFee.Value,
                Description = invoiceText,
                ArticleNr = InvoiceLineFee.RegularFee
            });

            // Check for late-payment and create an invoiceline
            if (feeSettings.STRSubmissionLatePaymentFeeExempt.GetValueOrDefault())
            {
                // Exempt for the late payment fee
            }
            else
            {
                var jurisdictionId = legalEntity.JurisdictionId.Value;
                var latePaymentFees = await _settingsManager.GetSTRLatePaymentFeesForJurisdictionAsync(jurisdictionId, submission.FinancialYear.Value);
                if (latePaymentFees != null)
                {
                    var theDate = DateTime.Today;
                    var actualLatePaymentFee = latePaymentFees.OrderByDescending(lpf => lpf.StartAt).FirstOrDefault(lpf => lpf.StartAt <= theDate && lpf.EndAt >= theDate);

                    if (actualLatePaymentFee != null && actualLatePaymentFee.Amount > 0)
                    {
                        invoiceText = actualLatePaymentFee.InvoiceText;
                        invoiceText = invoiceText.Replace("{year}", submission.FinancialYear.ToString(), StringComparison.OrdinalIgnoreCase);

                        invoiceLines.Add(new CreateInvoiceLine
                        {
                            Sequence = invoiceLines.Count + 1,
                            Amount = actualLatePaymentFee.Amount,
                            Description = invoiceText,
                            ArticleNr = InvoiceLineFee.LateFee
                        });
                    }
                }
            }

            // Create the invoice
            invoiceRequest.InvoiceLines = invoiceLines;

            var invoice = await _invoicesManager.CreateInvoiceAsync(invoiceRequest);
            submission.InvoiceId = invoice.Id;

            if (submission.IsPaid)
            {
                // Submission was already marked as paid
                await _invoicesManager.MarkInvoiceAsPaidAsync(invoice, true, false);
            }

            return invoice;
        }

        /// <summary>
        /// Gets the predicate expression based on the provided filter criteria.
        /// </summary>
        /// <param name="filter">The filter criteria.</param>
        /// <returns>The predicate expression.</returns>
        private Expression<Func<Submission, bool>> GetSubmissionPredicate(SearchSubmissionsRequest filter)
        {
            ArgumentNullException.ThrowIfNull(filter, nameof(filter));

            var moduleKey = GetModuleKey(filter.ModuleId);

            Expression<Func<Submission, bool>> predicate = s => s.Status != SubmissionStatus.Temporal;

            // Combine with the submission predicate using the authorized jurisdiction ids.
            predicate = predicate.And(_authorizationFilterExpressionFactory.GetSubmissionJurisdictionFilterPredicate(filter));

            predicate = predicate.And(submission => submission.ModuleId == filter.ModuleId);

            if (filter.FinancialYear != 0)
            {
                predicate = predicate.And(submission => submission.FinancialYear == filter.FinancialYear);
            }

            if (filter.SubmittedAfterDate.HasValue)
            {
                var date = filter.SubmittedAfterDate.Value.Date.ToUtcTime(moduleKey);
                predicate = predicate.And(submission => submission.SubmittedAt > date);
            }

            if (filter.SubmittedBeforeDate.HasValue)
            {
                var date = filter.SubmittedBeforeDate.Value.Date.ToUtcTime(moduleKey);
                predicate = predicate.And(submission => submission.SubmittedAt < date);
            }

            if (filter.IsPaid.HasValue)
            {
                predicate = predicate.And(submission => submission.IsPaid == filter.IsPaid.Value);
            }

            if (filter.IsExported.HasValue)
            {
                predicate = predicate.And(submission => submission.ExportedBy.HasValue == filter.IsExported.Value);
            }

            if (filter.Country != null)
            {
                var country = filter.Country == "no-country" ? "" : filter.Country;

                predicate = predicate.And(submission => submission.FormDocument.Attributes.Any(a =>
                    (a.Key == "address-of-head-office.country" && a.Value == country) ||
                    (a.Key == "address-of-head-office.nevisCountry" && a.Value == country) ||
                    (a.Key == "contact-information.country" && a.Value == country) ||
                    (a.Key == "tax-resident.residentCountry" && a.Value == country) ||
                    (a.Key == "finalize.country" && a.Value == country)));
            }

            return predicate;
        }

        /// <summary>
        /// Gets the predicate expression based on the provided filter criteria.
        /// </summary>
        /// <param name="filter">The filter criteria.</param>
        /// <returns>The predicate expression.</returns>
        private Expression<Func<Submission, bool>> GetSubmissionPredicate(FilterSubmissionsRequest filter)
        {
            ArgumentNullException.ThrowIfNull(filter, nameof(filter));

            var moduleKey = GetModuleKey(filter.ModuleId);

            Expression<Func<Submission, bool>> predicate = s => s.Status != SubmissionStatus.Temporal;

            // Combine with the submission predicate using the authorized jurisdiction ids.
            predicate = predicate.And(_authorizationFilterExpressionFactory.GetSubmissionJurisdictionFilterPredicate(filter));

            predicate = predicate.And(submission => submission.ModuleId == filter.ModuleId);

            predicate = AddLegalEntitySearchTermPredicate(predicate, filter);

            // Filter by the financial period
            // Filter by the start of the financial period
            if (filter.FinancialPeriodStartAt.HasValue)
            {
                predicate = predicate.And(submission => submission.StartsAt == filter.FinancialPeriodStartAt);
            }

            // Filter by the end of the financial period
            if (filter.FinancialPeriodEndAt.HasValue)
            {
                predicate = predicate.And(submission => submission.EndsAt == filter.FinancialPeriodEndAt);
            }

            if (filter.SubmittedAfterDate.HasValue)
            {
                var date = filter.SubmittedAfterDate.Value.Date.ToUtcTime(moduleKey);
                predicate = predicate.And(submission => submission.SubmittedAt > date);
            }

            if (filter.SubmittedBeforeDate.HasValue)
            {
                var date = filter.SubmittedBeforeDate.Value.Date.ToUtcTime(moduleKey);
                predicate = predicate.And(submission => submission.SubmittedAt < date);
            }

            if (filter.IsPaid.HasValue)
            {
                predicate = predicate.And(submission => submission.IsPaid == filter.IsPaid.Value);
            }

            if (filter.IsExported.HasValue)
            {
                predicate = predicate.And(submission => submission.ExportedBy.HasValue == filter.IsExported.Value);
            }

            if (filter.IsUsingAccountingRecordsTool.HasValue)
            {
                predicate = predicate.And(submission => submission.FormDocument.Attributes.Any(a =>
                    a.Key == WellKnownFormDocumentAttibuteKeys.UseAccountingTool &&
                    a.Value == filter.IsUsingAccountingRecordsTool.Value.ToString()));
            }

            return predicate;
        }

        /// <summary>
        /// Gets the predicate expression based on the provided filter criteria.
        /// </summary>
        /// <param name="filter">The filter criteria.</param>
        /// <returns>The predicate expression.</returns>
        private Expression<Func<Submission, bool>> GetSubmissionPredicate(FilterSubmissionsRequestForBahamas filter)
        {
            ArgumentNullException.ThrowIfNull(filter, nameof(filter));

            var moduleKey = GetModuleKey(filter.ModuleId);

            Expression<Func<Submission, bool>> predicate = s => s.Status != SubmissionStatus.Temporal;

            // Combine with the submission predicate using the authorized jurisdiction ids.
            predicate = predicate.And(_authorizationFilterExpressionFactory.GetSubmissionJurisdictionFilterPredicate(filter));

            predicate = predicate.And(submission => submission.ModuleId == filter.ModuleId);

            predicate = AddLegalEntityPredicate(predicate, filter);

            // Filter by status
            if (filter.Statuses != null && filter.Statuses.Any())
            {
                predicate = predicate.And(submission => filter.Statuses.Contains(submission.Status));
            }

            // Filter the submitted or not submissions
            if (filter.ShowSubmitted.HasValue)
            {
                if (filter.ShowSubmitted.Value)
                {
                    predicate = predicate.And(submission => submission.Status == SubmissionStatus.Submitted);
                }
                else
                {
                    predicate = predicate.And(submission => submission.Status != SubmissionStatus.Submitted);
                }
            }

            // Filter the submission that can be reopened
            if (filter.AllowReopen.HasValue)
            {
                if (filter.AllowReopen.Value)
                {
                    predicate = predicate.And(submission => submission.Status == SubmissionStatus.Submitted);
                }
                else
                {
                    predicate = predicate.And(submission => submission.Status != SubmissionStatus.Submitted);
                }
            }

            // Filter by the submitted date
            if (filter.SubmittedAfterDate.HasValue)
            {
                var date = filter.SubmittedAfterDate.Value.Date.ToUtcTime(moduleKey);
                predicate = predicate.And(submission => submission.SubmittedAt > date);
            }

            if (filter.SubmittedBeforeDate.HasValue)
            {
                var date = filter.SubmittedBeforeDate.Value.Date.ToUtcTime(moduleKey);
                predicate = predicate.And(submission => submission.SubmittedAt < date);
            }

            // Filter by the financial period
            // Filter by the start of the financial period
            if (filter.FinancialPeriodStartAt.HasValue)
            {
                predicate = predicate.And(submission => submission.StartsAt >= filter.FinancialPeriodStartAt);
            }

            // Filter by the end of the financial period
            if (filter.FinancialPeriodEndAt.HasValue)
            {
                predicate = predicate.And(submission => submission.EndsAt <= filter.FinancialPeriodEndAt);
            }

            // Filter by the paid
            if (filter.IsPaid.HasValue)
            {
                predicate = predicate.And(
                    submission => submission.IsPaid == filter.IsPaid.Value);
            }

            // Filter by the paid date
            if (filter.PaidAfterDate.HasValue)
            {
                predicate = predicate.And(
                    submission => submission.Invoice.PaymentInvoices.Where(
                        ip => ip.Payment.PaidAt.HasValue).OrderByDescending(ip => ip.Payment.PaidAt).FirstOrDefault().Payment.PaidAt >= filter.PaidAfterDate);
            }

            if (filter.PaidBeforeDate.HasValue)
            {
                predicate = predicate.And(
                    submission => submission.Invoice.PaymentInvoices.Where(
                        ip => ip.Payment.PaidAt.HasValue).OrderByDescending(ip => ip.Payment.PaidAt).FirstOrDefault().Payment.PaidAt <= filter.PaidAfterDate);
            }

            // Filter the exported submissions
            if (filter.IsExported.HasValue)
            {
                predicate = predicate.And(submission => submission.ExportedBy.HasValue == filter.IsExported.Value);
            }

            return predicate;
        }

        private async Task UpdateFormAttributes(KeyValueForm form, FormDocument formDocument)
        {
            // Update attributes
            var existingAttributes = formDocument.Attributes.ToList();

            var formValues = form.GetFormValues();
            foreach (var fieldValue in formValues)
            {
                var attr = existingAttributes.FirstOrDefault(x => x.Key == fieldValue.Key);
                if (attr != null)
                {
                    attr.Value = fieldValue.Value;
                    existingAttributes.Remove(attr);
                }
                else
                {
                    formDocument.Attributes.Add(new FormDocumentAttribute(fieldValue.Key, fieldValue.Key) { Value = fieldValue.Value });
                }
            }

            if (existingAttributes.Count > 0)
            {
                await _formDocumentAttributesRepository.DeleteAsync(existingAttributes);
            }
        }

        /// <summary>
        /// Get the the key of the module.
        /// </summary>
        /// <param name="moduleId">Id of the module to get the key for.</param>
        /// <returns>The key of the module.</returns>
        private string GetModuleKey(Guid moduleId)
        {
            var module = _modulesRepository.CheckModuleByIdAsync(moduleId).Result;
            return module.Key;
        }
    }
}