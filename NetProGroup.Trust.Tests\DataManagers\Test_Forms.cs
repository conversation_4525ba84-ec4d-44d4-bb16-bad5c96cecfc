﻿using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.DataManager.Forms;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class Test_Forms : TestBase
    {
        public Test_Forms()
        {
            SeedFormTemplates = false;
        }

        private IFormsDataManager _formsDataManager;

        private IMasterClientsRepository _masterClientsRepository;
        private ILegalEntitiesRepository _legalEntitiesRepository;
        private IFormTemplatesRepository _formTemplatesRepository;
        private IFormDocumentsRepository _formDocumentsRepository;

        private Guid _jurisdictionId = new Guid("BDEF352D-DEDC-4271-888D-EFA168404CE9");

        private Guid _legalEntityId;
        private Guid _templateId;
        private Guid _templateVersionId;
        private Guid _documentId;

        [SetUp]
        public void Setup()
        {
            _formsDataManager = _server.Services.GetRequiredService<IFormsDataManager>();

            _masterClientsRepository = _server.Services.GetRequiredService<IMasterClientsRepository>();
            _legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();
            _formTemplatesRepository = _server.Services.GetRequiredService<IFormTemplatesRepository>();
            _formDocumentsRepository = _server.Services.GetRequiredService<IFormDocumentsRepository>();

            Seed();
        }

        private void Seed()
        {
            // Create a masterClient
            var masterClient = new MasterClient
            {
                Code = "MCC 1"
            };
            _masterClientsRepository.Insert(masterClient, true);

            // Create a legal entity
            var legalEntity = new LegalEntity
            {
                MasterClientId = masterClient.Id,
                Name = "Company 1",
                Code = "1",
            };
            _legalEntitiesRepository.Insert(legalEntity, true);
            _legalEntityId = legalEntity.Id;

            // Create a form template
            var formTemplate = new FormTemplate
            {
                JurisdictionId = _jurisdictionId,
                Name = "Template 1",
                Key = "nevis.submission",
            };

            var form = new StandardForm();
            formTemplate.FormTemplateVersions.Add(new FormTemplateVersion { Name = "Template 1m v1.0", Version = "1.0", DataAsJson = new NetProGroup.Trust.Forms.FormBuilder { Form = form }.ToJson() });

            _formTemplatesRepository.Insert(formTemplate, true);

            _templateId = formTemplate.Id;
            _templateVersionId = formTemplate.FormTemplateVersions.First().Id;

            // Create form document
            var formDocument = new FormDocument
            {
                LegalEntityId = _legalEntityId,
                Name = "Document 1",
                Status = DomainShared.Enums.FormDocumentStatus.Draft
            };

            formDocument.FormDocumentRevisions.Add(new FormDocumentRevision { Revision = 1, Status = DomainShared.Enums.FormDocumentRevisionStatus.Draft });

            _formDocumentsRepository.Insert(formDocument, true);

            _documentId = formDocument.Id;
        }

        [Test]
        public async Task Test_List_Form_Templates_Success()
        {
            // Arrange
            var request = new DataManager.Forms.RequestResponses.ListFormTemplatesRequest { JurisdictionId = _jurisdictionId };

            // Act
            var response = await _formsDataManager.ListFormTemplatesAsync(request);

            // Assert
            Assert.That(response.FormTemplateItems.Count(), Is.EqualTo(1));
        }

        [Test]
        public async Task Test_List_Form_Documents_Success()
        {
            // Arrange
            var request = new DataManager.Forms.RequestResponses.ListFormDocumentsRequest { LegalEntityId = _legalEntityId };

            // Act
            var response = await _formsDataManager.ListFormDocumentsAsync(request);

            // Assert
            Assert.That(response.FormDocumentItems.Count(), Is.EqualTo(1));
        }

        [Test]
        public async Task Test_Get_Form_Template_Success()
        {
            // Arrange
            var request = new DataManager.Forms.RequestResponses.GetFormTemplateRequest { FormTemplateId = _templateId };

            // Act
            var response = await _formsDataManager.GetFormTemplateAsync(request);

            // Assert
            Assert.That(response.FormTemplate, Is.Not.Null);
            Assert.That(response.FormTemplate.Versions.Count(), Is.EqualTo(1));
        }

        [Test]
        public async Task Test_Get_Form_Document_Success()
        {
            // Arrange
            var request = new DataManager.Forms.RequestResponses.GetFormDocumentRequest { FormDocumentId = _documentId, Revision = null, GetAllRevisions = true };

            // Act
            var response = await _formsDataManager.GetFormDocumentAsync(request);

            // Assert
            Assert.That(response.FormDocument, Is.Not.Null);
            Assert.That(response.FormDocument.Revisions.Count(), Is.EqualTo(1));
        }

        [Test]
        public void Test_Get_Form_Document_No_Id_Fails()
        {
            // Arrange
            var request = new DataManager.Forms.RequestResponses.GetFormDocumentRequest();

            // Act/Assert
            Assert.ThrowsAsync<ArgumentException>(async () => { await _formsDataManager.GetFormDocumentAsync(request); });
        }

        [Test]
        public void Test_List_Form_Document_No_LegaEntityId_Fails()
        {
            // Arrange
            var request = new DataManager.Forms.RequestResponses.ListFormDocumentsRequest();

            // Act/Assert
            Assert.ThrowsAsync<ArgumentException>(async () => { await _formsDataManager.ListFormDocumentsAsync(request); });
        }

        [Test]
        public void Test_List_Form_Template_No_JurisdictionId_Fails()
        {
            // Arrange
            var request = new DataManager.Forms.RequestResponses.ListFormTemplatesRequest();

            // Act/Assert
            Assert.ThrowsAsync<ArgumentException>(async () => { await _formsDataManager.ListFormTemplatesAsync(request); });
        }

        [Test]
        public void Test_Get_Form_Template_No_Id_Fails()
        {
            // Arrange
            var request = new DataManager.Forms.RequestResponses.GetFormTemplateRequest();

            // Act/Assert
            Assert.ThrowsAsync<ArgumentException>(async () => { await _formsDataManager.GetFormTemplateAsync(request); });
        }

        [Test]
        public void Test_Create_FormDocument()
        {
            // Arrange
            var request = new DataManager.Forms.RequestResponses.CreateFormDocumentRequest
            {
                LegalEntityId = _legalEntityId,
                FormTemplateVersionId = _templateVersionId,
                Form = new StandardForm(),
                Name = null
            };

            // Act
            var response = _formsDataManager.CreateFormDocumentAsync(request).Result;

            // Assert
            Assert.That(response.FormDocument, Is.Not.Null);
            Assert.That(response.FormDocument.Revisions.Count(), Is.EqualTo(1));
        }
    }
}
