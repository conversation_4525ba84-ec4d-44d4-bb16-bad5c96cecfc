// <copyright file="AuthorizationFilterExpressionFactory.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Submissions;
using System.Linq.Expressions;

namespace NetProGroup.Trust.DataManager.Security
{
    /// <inheritdoc />
    public class AuthorizationFilterExpressionFactory : IAuthorizationFilterExpressionFactory, ITransientService
    {
        /// <inheritdoc />
        public Expression<Func<LegalEntity, bool>> GetLegalEntityJurisdictionFilterPredicate(IJurisdictionFilteredRequest request)
        {
            return le => le.JurisdictionId.HasValue && request.AuthorizedJurisdictionIDs.Contains(le.JurisdictionId.Value);
        }

        /// <inheritdoc />
        public Expression<Func<Jurisdiction, bool>> GetJurisdictionJurisdictionFilterPredicate(IJurisdictionFilteredRequest request)
        {
            return j => request.AuthorizedJurisdictionIDs.Contains(j.Id);
        }

        /// <inheritdoc />
        public Expression<Func<FormTemplate, bool>> GetFormTemplateJurisdictionFilterPredicate(IJurisdictionFilteredRequest request)
        {
            return ft => request.AuthorizedJurisdictionIDs.Contains(ft.JurisdictionId);
        }

        /// <inheritdoc />
        public Expression<Func<MasterClient, bool>> GetMasterClientUserIdFilterPredicate(IUserIdFilteredRequest request)
        {
            return mc => mc.MasterClientUsers.Any(mcu => mcu.UserId == request.UserId);
        }

        /// <inheritdoc />
        public Expression<Func<Invoice, bool>> GetInvoiceUserIdFilterPredicate(IUserIdFilteredRequest request)
        {
            var masterClientPredicate = GetMasterClientUserIdFilterPredicate(request);

            return ExpressionExtensions.Combine<Invoice, MasterClient, bool>(i => i.LegalEntity.MasterClient, masterClientPredicate);
        }

        /// <inheritdoc />
        public Expression<Func<Domain.Payments.Payment, bool>> GetPaymentUserIdFilterPredicate(IUserIdFilteredRequest request)
        {
            var masterClientPredicate = GetMasterClientUserIdFilterPredicate(request);

            return ExpressionExtensions.Combine<Domain.Payments.Payment, MasterClient, bool>(p => p.LegalEntity.MasterClient, masterClientPredicate).Or(
                ExpressionExtensions.Combine<Domain.Payments.Payment, MasterClient, bool>(p => p.MasterClient, masterClientPredicate));
        }

        /// <inheritdoc/>
        public Expression<Func<Submission, bool>> GetSubmissionJurisdictionFilterPredicate(IJurisdictionFilteredRequest request)
        {
            var legalEntityPredicate = GetLegalEntityJurisdictionFilterPredicate(request);
            return ExpressionExtensions.Combine<Submission, LegalEntity, bool>(s => s.LegalEntity, legalEntityPredicate);
        }

        /// <inheritdoc/>
        public Expression<Func<RequestForInformation, bool>> GetRequestForInformationJurisdictionFilterPredicate(IJurisdictionFilteredRequest request)
        {
            var legalEntityPredicate = GetLegalEntityJurisdictionFilterPredicate(request);
            return ExpressionExtensions.Combine<RequestForInformation, LegalEntity, bool>(r => r.Submission.LegalEntity, legalEntityPredicate);
        }
    }
}