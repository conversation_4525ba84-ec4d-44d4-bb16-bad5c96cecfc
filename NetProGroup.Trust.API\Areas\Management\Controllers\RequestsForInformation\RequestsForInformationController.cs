// <copyright file="RequestsForInformationController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.RequestsForInformation;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.RequestsForInformation
{
    /// <summary>
    /// Use this controller for request for information related methods.
    /// </summary>
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/requests-for-information")]
    public class RequestsForInformationController : TrustAPIControllerBase
    {
        private readonly IRequestsForInformationAppService _requestsForInformationAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="RequestsForInformationController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="requestsForInformationAppService">The service for requests for information.</param>
        public RequestsForInformationController(
            ILogger<RequestsForInformationController> logger,
            IRequestsForInformationAppService requestsForInformationAppService)
            : base(logger)
        {
            _requestsForInformationAppService = requestsForInformationAppService;
        }

        /// <summary>
        /// Gets the list of all request for information that match the criteria.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/requests-for-information.
        /// </remarks>
        /// <param name="request">Request model holding paging and searching parameters.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the submissions.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "Management_ListRFIs")]
        [ProducesResponseType(typeof(PaginatedResponse<ListSubmissionRFIDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListAllRFISubmissions([FromQuery] ListRequestsForInformationDTO request)
        {
            var result = await ProcessRequestWithPagedResponseAsync(
                validate: () =>
                {
                },

                 executeAsync: async _ =>
                 {
                     return await _requestsForInformationAppService.ListRequestsForInformationAsync(request);
                 });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the RFI with the given id.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/requests-for-information/{requestForInformationId}.
        /// </remarks>
        /// <param name="requestForInformationId">The id of the required RFI as Guid.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the RFI.</returns>
        [HttpGet("{requestForInformationId}")]
        [SwaggerOperation(OperationId = "Management_GetRFI")]
        [ProducesResponseType(typeof(RequestForInformationDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetRequestForInformation(Guid requestForInformationId)
        {
            RequestForInformationDTO item = null;

            var result = await ProcessRequestAsync<RequestForInformationDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(requestForInformationId, nameof(requestForInformationId));
                },

                executeAsync: async () =>
                {
                    item = await _requestsForInformationAppService.GetRequestForInformationByIdAsync(requestForInformationId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the list of submissions with created RFI requests that match the criteria.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/requests-for-information/submissions.
        /// </remarks>
        /// <param name="request">Request model holding paging and searching parameters.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the submissions.</returns>
        [HttpGet("submissions")]
        [SwaggerOperation(OperationId = "Management_ListRFISubmissions")]
        [ProducesResponseType(typeof(PaginatedResponse<ListSubmissionRFIDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ListRFISubmissions([FromQuery] ListRequestsForInformationDTO request)
        {
            var result = await ProcessRequestWithPagedResponseAsync(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(request.LegalEntityId, nameof(request.LegalEntityId));
                    Check.NotDefaultOrNull<Guid>(request.ModuleId, nameof(request.ModuleId));
                },

                 executeAsync: async _ =>
                 {
                     return await _requestsForInformationAppService.ListRequestsForInformationAsync(request);
                 });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the given submission with the request for information and financial period changes details added.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/requests-for-information/submissions/{submissionId}/details.
        ///
        /// </remarks>
        /// <param name="submissionId">The id of the submission to get.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="SubmissionRFIDetailsDTO"/>.</returns>
        [HttpGet("submissions/{submissionId}/details")]
        [SwaggerOperation(OperationId = "Management_GetSubmissionRFIDetails")]
        [ProducesResponseType(typeof(SubmissionRFIDetailsDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSubmissionRFIDetailsById(Guid submissionId)
        {
            SubmissionRFIDetailsDTO item = null;

            var result = await ProcessRequestAsync<SubmissionRFIDetailsDTO>(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _requestsForInformationAppService.GetRFISubmissionDetailsAsync(submissionId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Create a new request for information for a submission.
        /// </summary>
        /// <remarks>
        ///
        /// Sample request:
        ///
        ///     POST /api/v1/management/requests-for-information.
        /// </remarks>
        /// <param name="data">The necessary data used to create a request for information.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation of FileContentResult.</returns>
        [HttpPost]
        [SwaggerOperation(OperationId = "Management_Rfi_Create")]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateRequestForInformation(
            CreateRFIDTO data)
        {
            Guid item = Guid.Empty;
            var result = await ProcessRequestAsync<Guid>(
                validate: () =>
                {
                    Check.NotNull(data, nameof(data));
                    Check.NotDefaultOrNull<Guid>(data.SubmissionId, nameof(data.SubmissionId));
                    Check.NotNullOrEmpty(data.Comments, nameof(data.Comments));
                    Check.NotDefaultOrNull<DateTime>(data.DeadLine, nameof(data.DeadLine));
                    ValidateWorkContextUserId();
                },
                executeAsync: async () =>
                {
                    item = await _requestsForInformationAppService.CreateRequestForInformationAsync(data.SubmissionId, data);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Upload a document for a request for information.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///     POST /api/v1/management/requests-for-information/{requestForInformationId}/documents
        ///     {
        ///         data
        ///     }.
        /// </remarks>
        /// <param name="requestForInformationId">The id of the request for information as Guid.</param>
        /// <param name="data">The data necessary to create a request for information document.</param>
        /// <response code="200">OK.</response>
        /// <response code="401">Unauthorized.</response>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost("{requestForInformationId}/documents")]
        [SwaggerOperation(OperationId = "Management_Rfi_Document_Create", Summary = "Upload a document for a request for information")]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateRequestForinformationDocument(
            Guid requestForInformationId,
            [FromForm]
            CreateRFIDocumentDTO data)
        {
            var result = await ProcessRequestAsync<object>(

                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(requestForInformationId, nameof(requestForInformationId));
                    Check.NotNull(data, nameof(data));
                    Check.NotNull(data.File, nameof(data.File));
                },

                executeAsync: async () =>
                {
                    await _requestsForInformationAppService.CreateRFIDocumentByManagementAsync(requestForInformationId, data);
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Cancels the submission.
        /// </summary>
        /// <remarks>
        /// This will mark the submission as cancelled.
        ///
        /// Sample request:
        ///
        ///     PUT /api/v1/management/requests-for-information/{requestForInformationId}/cancelled
        ///         {
        ///
        ///         }.
        ///
        /// </remarks>
        /// <param name="requestForInformationId">The id of the request for information to delete as Guid.</param>
        /// <param name="reason">The reason for cancelling the RFI.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        [HttpPut("{requestForInformationId}/cancelled")]
        [SwaggerOperation(OperationId = "Management_Cancel_Rfi")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status412PreconditionFailed)]
        public async Task<IActionResult> CancelRequestForInformation(
            Guid requestForInformationId, string reason)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(requestForInformationId, nameof(requestForInformationId));
                },
                executeAsync: async () =>
                {
                    await _requestsForInformationAppService.CancelRequestForInformationAsync(requestForInformationId, reason);
                });

            return result.AsResponse();
        }
    }
}