﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class RequestForInformationActionFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "CompletedBy",
                table: "RequestForInformation",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Remark",
                table: "RequestForInformation",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "RepliedAt",
                table: "RequestForInformation",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "RepliedBy",
                table: "RequestForInformation",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_RequestForInformation_CompletedBy",
                table: "RequestForInformation",
                column: "CompletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_RequestForInformation_RepliedBy",
                table: "RequestForInformation",
                column: "RepliedBy");

            migrationBuilder.AddForeignKey(
                name: "FK_RequestForInformation_Users_CompletedBy",
                table: "RequestForInformation",
                column: "CompletedBy",
                principalSchema: "NetPro",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_RequestForInformation_Users_RepliedBy",
                table: "RequestForInformation",
                column: "RepliedBy",
                principalSchema: "NetPro",
                principalTable: "Users",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RequestForInformation_Users_CompletedBy",
                table: "RequestForInformation");

            migrationBuilder.DropForeignKey(
                name: "FK_RequestForInformation_Users_RepliedBy",
                table: "RequestForInformation");

            migrationBuilder.DropIndex(
                name: "IX_RequestForInformation_CompletedBy",
                table: "RequestForInformation");

            migrationBuilder.DropIndex(
                name: "IX_RequestForInformation_RepliedBy",
                table: "RequestForInformation");

            migrationBuilder.DropColumn(
                name: "CompletedBy",
                table: "RequestForInformation");

            migrationBuilder.DropColumn(
                name: "Remark",
                table: "RequestForInformation");

            migrationBuilder.DropColumn(
                name: "RepliedAt",
                table: "RequestForInformation");

            migrationBuilder.DropColumn(
                name: "RepliedBy",
                table: "RequestForInformation");
        }
    }
}
