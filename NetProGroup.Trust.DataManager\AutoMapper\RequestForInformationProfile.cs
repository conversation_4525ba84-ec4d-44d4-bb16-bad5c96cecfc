﻿// <copyright file="RequestForInformationProfile.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Application.Contracts.RequestsForInformation;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using Twilio.Rest.Messaging.V1.BrandRegistration;
using NetProGroup.Trust.Application.Contracts.Shared;

namespace NetProGroup.Trust.DataManager.AutoMapper
{
    /// <summary>
    /// The submissions profile for AutoMapper.
    /// </summary>
    public class RequestForInformationProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="RequestForInformationProfile"/> class.
        /// </summary>
        public RequestForInformationProfile()
        {
            CreateMap<RequestForInformation, RequestForInformationDTO>()
                .ForMember(dest => dest.CompletedBy,
                    opt => opt.MapFrom(src => src.CompletedByUser.Email))
                .ForMember(dest => dest.RepliedBy,
                    opt => opt.MapFrom(src => src.RepliedByUser.Email))
                .ForMember(dest => dest.CreatedBy,
                    opt => opt.MapFrom(src => src.CreatedByUser.Email))
                .ForMember(dest => dest.LegalEntityName,
                    opt => opt.MapFrom(src => src.Submission.LegalEntity.Name))
                .ForMember(dest => dest.JurisdictionName,
                    opt => opt.MapFrom(src => src.Submission.LegalEntity.Jurisdiction.Name))
                .ForMember(dest => dest.JurisdictionCode,
                    opt => opt.MapFrom(src => src.Submission.LegalEntity.Jurisdiction.Code))
                .ForMember(dest => dest.MasterClientName,
                    opt => opt.MapFrom(src => src.Submission.LegalEntity.MasterClient.Name))
                ;

            CreateMap<RequestForInformation, ListSubmissionRFIDTO>()
                .ForMember(dest => dest.LegalEntityName,
                    opt => opt.MapFrom(src => src.Submission.LegalEntity.Name))
                .ForMember(dest => dest.LegalEntityCode,
                    opt => opt.MapFrom(src => src.Submission.LegalEntity.LegacyCode ?? src.Submission.LegalEntity.Code))
                .ForMember(dest => dest.MasterClientCode,
                    opt => opt.MapFrom(src => src.Submission.LegalEntity.MasterClient.Code))

                // FinancialPeriods
                .ForMember(dest => dest.FinancialPeriodStartsAt,
                    opt => opt.MapFrom(src => src.Submission.StartsAt))
                .ForMember(dest => dest.FinancialPeriodEndsAt,
                    opt => opt.MapFrom(src => src.Submission.EndsAt))
                .ForMember(dest => dest.Status,
                    opt => opt.MapFrom(src => src.Submission.Status))
                .ForMember(dest => dest.ExportedAt,
                    opt => opt.MapFrom(src => src.Submission.ExportedAt))

                // RFI
                .ForMember(dest => dest.RFICreatedAt,
                    opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(dest => dest.RFIDeadLine,
                    opt => opt.MapFrom(src => src.DeadLine))
                .ForMember(dest => dest.RFICompletedAt,
                    opt => opt.MapFrom(src => src.CompletedAt))
                .ForMember(dest => dest.RFILastReminderSentAt,
                    opt => opt.MapFrom(src => src.LastRemindedAt))
                .ForMember(dest => dest.RFIStatus,
                    opt => opt.MapFrom(src => src.Status))
                ;

            CreateMap<ListRequestsForInformationDTO, ListRequestsForInformationRequest>()
                .ForMember(dest => dest.AuthorizedJurisdictionIDs, opt => opt.Ignore())
                ;
        }
    }
}
