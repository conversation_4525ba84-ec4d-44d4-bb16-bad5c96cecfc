// <copyright file="RequestsForInformationAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Documents;
using NetProGroup.Trust.Application.Contracts.RequestsForInformation;
using NetProGroup.Trust.DataManager.Modules;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.Shared.Permissions;
using X.PagedList;

namespace NetProGroup.Trust.Application.AppServices.RequestsForInformation
{
    /// <summary>
    /// Application service for requests for information.
    /// </summary>
    public class RequestsForInformationAppService : IRequestsForInformationAppService
    {
        private readonly IWorkContext _workContext;
        private readonly IDocumentManager _documentManager;
        private readonly IModulesDataManager _modulesDataManager;
        private readonly ISubmissionsManager _submissionsManager;
        private readonly IRequestForInformationManager _requestForInformationManager;
        private readonly ISecurityManager _securityManager;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="RequestsForInformationAppService"/> class.
        /// </summary>
        /// <param name="workContext">Instance of the current WorkContext.</param>
        /// <param name="documentManager">An instance of IDocumentManager.</param>
        /// <param name="modulesDataManager">The manager for modules.</param>
        /// <param name="requestForInformationManager">the manager for requests for information.</param>
        /// <param name="submissionsManager">The manager to use for submissions.</param>
        /// <param name="securityManager">The security manager.</param>
        /// <param name="mapper">An instance of the mapper class.</param>
        public RequestsForInformationAppService(IWorkContext workContext,
                                     IDocumentManager documentManager,
                                     IModulesDataManager modulesDataManager,
                                     ISubmissionsManager submissionsManager,
                                     IRequestForInformationManager requestForInformationManager,
                                     ISecurityManager securityManager,
                                     IMapper mapper)
        {
            _workContext = workContext;

            _documentManager = documentManager;
            _modulesDataManager = modulesDataManager;
            _submissionsManager = submissionsManager;
            _securityManager = securityManager;
            _requestForInformationManager = requestForInformationManager;
            _mapper = mapper;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListSubmissionRFIDTO>> ListRequestsForInformationAsync(ListRequestsForInformationDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var filterRequest = _mapper.Map<ListRequestsForInformationRequest>(request);
            filterRequest.AuthorizedJurisdictionIDs = await _securityManager.GetJurisdictionsForManagementPermissionAsync(WellKnownPermissionNames.RFI_Module_View);

            return await _requestForInformationManager.ListRequestForInformationAsync(filterRequest);
        }

        /// <inheritdoc/>
        public async Task<RequestForInformationDTO> GetRequestForInformationByIdAsync(Guid requestForInformationId)
        {
            ArgumentNullException.ThrowIfNull(requestForInformationId, nameof(requestForInformationId));

            await _securityManager.RequireManagementPermissionForRequestForInformationAsync(WellKnownPermissionNames.RFI_Module_View, requestForInformationId);

            return await _requestForInformationManager.GetRequestForInformationByIdAsync(requestForInformationId);
        }

        /// <inheritdoc/>
        public async Task<Guid> CreateRequestForInformationAsync(Guid submissionId, CreateRFIDTO data)
        {
            // Check the user permission
            await CheckUserPermissionBySubmissionId(submissionId);

            return await _requestForInformationManager.CreateRequestForInformationAsync(submissionId, data);
        }

        /// <inheritdoc/>
        public async Task<SubmissionRFIDetailsDTO> GetRFISubmissionDetailsAsync(Guid submissionId)
        {
            var userIsClient = await _securityManager.UserIsClient();

            if (!userIsClient)
            {
                var moduleKey = await _submissionsManager.GetModuleKeyForSubmissionAsync(submissionId);
                await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.RFI_Module_View);
            }
            else
            {
                await _securityManager.RequireClientAccessToSubmissionAsync(submissionId);
            }

            return await _requestForInformationManager.GetRFISubmissionDetailsAsync(submissionId);
        }

        /// <inheritdoc/>
        public async Task CreateRFIDocumentByManagementAsync(Guid requestForInformationId, CreateRFIDocumentDTO data)
        {
            // Retreive the RFI entity.
            var requestForInformation = await _requestForInformationManager.GetRFIById(requestForInformationId);

            // Check the user permission
            await CheckUserPermissionBySubmissionId(requestForInformation.SubmissionId);

            await _requestForInformationManager.CreateRFIDocumentAsync(requestForInformationId, data, createdByManagementUser: true);
        }

        /// <inheritdoc/>
        public async Task CreateRequestForInformationDocumentByClientAsync(Guid requestForInformationId, CreateRFIDocumentDTO data)
        {
            // Retreive the RFI entity.
            var requestForInformation = await _requestForInformationManager.GetRFIById(requestForInformationId);

            await _securityManager.RequireClientAccessToSubmissionAsync(requestForInformation.SubmissionId);

            await _requestForInformationManager.CreateRFIDocumentAsync(requestForInformationId, data, createdByManagementUser: false);
        }

        /// <inheritdoc/>
        public async Task CancelRequestForInformationAsync(Guid requestForInformationId, string reason)
        {
            var requestForInformation = await _requestForInformationManager.GetRFIById(requestForInformationId);

            var moduleKey = await _submissionsManager.GetModuleKeyForSubmissionAsync(requestForInformation.SubmissionId);

            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.RFI_Cancel);

            await _requestForInformationManager.CancelRequestForInformationAsync(requestForInformationId, reason);
        }

        /// <inheritdoc/>
        public async Task CompleteRequestForInformationAsync(Guid requestForInformationId, CompleteRequestForInformationDTO data)
        {
            // Retreive the RFI entity.
            var requestForInformation = await _requestForInformationManager.GetRFIById(requestForInformationId);

            await _securityManager.RequireClientAccessToSubmissionAsync(requestForInformation.SubmissionId);

            await _requestForInformationManager.CompleteRequestForInformationAsync(requestForInformationId, data);
        }

        #region Private methods

        /// <summary>
        /// Check the RFI permission given the submission id and returns the module key.
        /// </summary>
        /// <param name="submissionId">THe submission id as Guid.</param>
        private async Task<string> CheckUserPermissionBySubmissionId(Guid submissionId)
        {
            var moduleKey = await _submissionsManager.GetModuleKeyForSubmissionAsync(submissionId);
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.RFI_Complete);
            return moduleKey;
        }
        #endregion
    }
}