import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  ScrollArea,
  ScrollBar,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@netpro/design-system";
import type { ReactNode } from "react";
import type { UseFormReturn } from "react-hook-form";
import { Form as RemixForm } from "@remix-run/react"
import { CurrencyInput } from "~/components/ui/inputs/CurrencyInput";
import { Currency } from "~/lib/basic-financial-report/utilities/currencies";
import type { CashBankAccountSchemaType } from "~/lib/basic-financial-report/types/current-assets-details-schema";
import { AccountType, SHOW_BANK_NAME } from "~/lib/basic-financial-report/types/current-assets-details-schema";

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit: (data: CashBankAccountSchemaType) => void
  form: UseFormReturn<CashBankAccountSchemaType>
};

const FORM_ID = "cash-bank-account-dialog-form"

export function CashBankAccountDialog({
  open,
  setOpen,
  onSubmit,
  form,
}: Props): ReactNode {
  const watchAccountType = form.watch("accountType") as AccountType;

  return (
    <Dialog open={open} onOpenChange={setOpen} modal>
      <DialogContent
        className="max-w-screen-sm"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <ScrollArea className="pr-3">
          <Form {...form}>
            <RemixForm onSubmit={form.handleSubmit(onSubmit)} className="p-2" noValidate id={FORM_ID}>
              <DialogHeader>
                <DialogTitle>Cash/Bank Account</DialogTitle>
              </DialogHeader>
              <div className="flex-col space-y-2 pt-4">

                <FormField
                  control={form.control}
                  name="accountType"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>
                        <p>
                          Account Type*
                        </p>
                      </FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                          disabled={field.disabled}
                        >
                          <FormControl className="md:w-1/2 sm:w-full">
                            <SelectTrigger invalid={!!fieldState.error}>
                              <SelectValue placeholder="Select an option" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            { Object.values(AccountType).map(year => (
                              <SelectItem key={year} value={year.toString()}>
                                {year}
                              </SelectItem>
                            )) }
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {SHOW_BANK_NAME.includes(watchAccountType) && (
                  <FormField
                    control={form.control}
                    name="bankName"
                    render={({ field, fieldState }) => (
                      <FormItem className="md:w-1/2 sm:w-full">
                        <FormLabel>Bank Name*</FormLabel>
                        <FormControl>
                          <Input
                            invalid={!!fieldState.error}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel> Amount*</FormLabel>
                      <FormControl>
                        <CurrencyInput
                          currencyName={Currency.USD}
                          invalid={!!fieldState.error}
                          {...field}
                          type="number"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter className="pt-4">
                <div className="flex w-full justify-end">
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => setOpen(false)} type="button">Cancel</Button>
                    <Button size="sm" variant="default" type="submit" form={FORM_ID}>Save</Button>
                  </div>
                </div>
              </DialogFooter>
            </RemixForm>
          </Form>
          <ScrollBar />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
