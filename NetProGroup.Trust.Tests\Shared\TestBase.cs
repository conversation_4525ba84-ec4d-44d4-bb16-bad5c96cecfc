﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.DependencyInjection.Extensions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.Application.AppServices.Jurisdictions;
using NetProGroup.Trust.Application.Contracts.FeatureFlags;
using NetProGroup.Trust.DataManager.AutoMapper;
using NetProGroup.Trust.DataManager.Bulk;
using NetProGroup.Trust.DataManager.Configurations;
using NetProGroup.Trust.DataManager.Forms;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.Import.Importers;
using NetProGroup.Trust.Payment.Provider;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Template;
using NetProGroup.Trust.Shared.Jurisdictions;
using System.Data.Entity;

namespace NetProGroup.Trust.Tests.Shared
{
    public abstract class TestBase
    {
        private IRoleRepository _roleRepository;
        private IUserManager _userManager;
        protected TestServer _server;
        protected HttpClient HttpClient;
        protected ApplicationUserDTO ManagementUser;
        protected ApplicationUserDTO ClientUser;
        protected ApplicationUserDTO BasicUser;
        protected ApplicationUserDTO COMOwner;
        protected MasterClient _masterClient;
        protected Guid _superAdminRoleId = Guid.Parse("{F7B327DB-4633-484A-B8FF-63DEA19FAC02}");
        protected Guid _basicUserRoleId = Guid.Parse("{47F0BEE9-F846-46BD-B136-835BC7CC9E40}");
        private Guid _supportRoleId = Guid.Parse("{0a31ffd4-5249-4341-81df-73058a820076}");
        private Guid _bahamasOwnerRoleId = Guid.Parse("{3f15b6d5-a425-4aff-bf49-39ee9327f213}");
        private Guid _commonComOwner = Guid.Parse("{3f13b6d5-a225-4aff-bf48-39fe9327f213}");
        protected Guid ModuleStrId;
        protected Guid ModuleBfrId;
        protected Guid ModuleEsId;
        protected Guid ModuleEsBviId;
        protected Guid JurisdictionNevisId;
        protected Guid JurisdictionBahamasId;
        protected Guid JurisdictionPanamaId;
        protected Jurisdiction JurisdictionNevis;
        protected Jurisdiction JurisdictionBahamas;
        protected Jurisdiction JurisdictionPanama;
        protected bool SeedFormTemplates = true;
        protected readonly string _clientUserEmail = "<EMAIL>";

        [SetUp]
        public async Task SetupBase()
        {
            var config = new ConfigurationBuilder()
                .SetBasePath(TestContext.CurrentContext.TestDirectory)
                .AddJsonFile("appsettings.json", optional: true)
                .AddJsonFile("appsettings.Test.json", optional: false)
                .AddEnvironmentVariables()
                .AddUserSecrets<TestBase>()
                .Build();

            _server = new TestServer(
                new WebHostBuilder().UseEnvironment("Development")
                    .UseConfiguration(config)
                    .UseStartup<Startup>()
                    .ConfigureTestServices((services) => ConfigureTestServices(services))
            );

            HttpClient = _server.CreateClient();
            _roleRepository = _server.Services.GetRequiredService<IRoleRepository>();
            _userManager = _server.Services.GetRequiredService<IUserManager>();
            await OnSetup();
        }

        public async Task SetUpUserRolesAsync(ApplicationUserDTO user, List<string> roleNames)
        {
            var roles = new List<ApplicationRole>();

            foreach (var roleName in roleNames)
            {
                var role = await _roleRepository.GetRoleByPredicateAsync(r => r.Name == roleName);
                if (role == null)
                {
                    throw new InvalidOperationException($"Role '{roleName}' not found.");
                }

                roles.Add(role);
            }

            var roleIds = roles.Select(r => r.Id).ToList();
            await _userManager.SetUserRolesAsync(user.Id, roleIds, user);
        }

        protected virtual async Task OnSetup()
        {
            await CreateDatabase();

            await Seed();
        }

        private async Task CreateDatabase()
        {
            const int maxRetries = 10;
            for (int i = 0; i < maxRetries; i++)
            {
                var dbContext = _server.Services.GetRequiredService<TrustDbContext>();
                await dbContext.Database.EnsureDeletedAsync();
                await dbContext.Database.EnsureCreatedAsync();

                if (dbContext.Set<Jurisdiction>().Any())
                {
                    // Jurisdictions are seeded and available
                    return;
                }
            }

            throw new InvalidOperationException(
                $"Jurisdictions were not seeded properly after {maxRetries} retries. " +
                "This indicates an issue with the database seeding mechanism.");
        }

        /// <summary>
        /// Waits for the jurisdictions to be seeded and available in the database.
        /// This should not be necessary, but the first test sometimes fails because the in-memory database does
        /// not have the seeded base data yet.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task WaitForJurisdictionsToBeSeeded()
        {
            var dbContext = _server.Services.GetRequiredService<TrustDbContext>();

            const int maxRetries = 100;
            for (int i = 0; i < maxRetries; i++)
            {
                if ((dbContext.Set<Jurisdiction>().Any()))
                {
                    // Jurisdictions are seeded and available
                    return;
                }

                await Task.Delay(10);
            }

            throw new InvalidOperationException(
                $"Jurisdictions were not seeded properly after {maxRetries} retries. " +
                "This indicates an issue with the database seeding mechanism.");
        }

        protected void SetWorkContextUser(ApplicationUserDTO user)
        {
            var workContext = _server.Services.GetRequiredService<IWorkContext>();

            workContext.IdentityUserId = user.Id;
            workContext.User = user;
        }

        protected virtual void ConfigureTestServices(IServiceCollection services)
        {
            services.AddApplicationInsightsTelemetry();

            // Register services from assemblies
            var assemblies = new List<System.Reflection.Assembly>
            {
                typeof(JurisdictionsAppService).Assembly,
                typeof(ApplicationProfile).Assembly,
                typeof(MasterClientImport).Assembly,
                typeof(IJurisdictionsRepository).Assembly,
                typeof(TrustDbContext).Assembly,
                typeof(IPaymentProvider).Assembly,
                typeof(IExcelTemplateService<>).Assembly
            };
            services.RegisterServices(assemblies.ToArray());

            // Register the SimpleBulkOperationProvider for tests
            services.AddSingleton<IBulkOperationProvider, SimpleBulkOperationProvider>();
            services.AddTransient<TestSeeder>();
            services.AddScoped<IWorkContext, TestWorkContext>();
        }

        private async Task Seed()
        {
            var jurisdictionRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();
            var jurisdictionNevis = (await jurisdictionRepository.FindByConditionAsync(j => j.Code == JurisdictionCodes.Nevis)).Single();
            var jurisdictionBahamas = (await jurisdictionRepository.FindByConditionAsync(j => j.Code == JurisdictionCodes.Bahamas)).Single();
            var jurisdictionPanama = (await jurisdictionRepository.FindByConditionAsync(j => j.Code == JurisdictionCodes.Panama)).Single();

            JurisdictionNevis = jurisdictionNevis;
            JurisdictionBahamas = jurisdictionBahamas;
            JurisdictionPanama = jurisdictionPanama;
            JurisdictionNevisId = jurisdictionNevis.Id;
            JurisdictionBahamasId = jurisdictionBahamas.Id;
            JurisdictionPanamaId = jurisdictionPanama.Id;

            await SetupModulesAsync();

            if (SeedFormTemplates)
            {
                await CreateFormTemplatesAsync();
            }

            var seeder = _server.Services.GetRequiredService<TestSeeder>();
            await seeder.RunAsync();

            // Users
            var userManager = _server.Services.GetRequiredService<IUserManager>();

            await userManager.CreateRoleAsync(new ApplicationRoleDTO
            {
                Id = _superAdminRoleId,
                Name = WellKnownRoleNames.Common_SuperAdmin,
                DisplayName = WellKnownRoleNames.Common_SuperAdmin
            });

            await userManager.CreateRoleAsync(new ApplicationRoleDTO
            {
                Id = _basicUserRoleId,
                Name = WellKnownRoleNames.Common_BasicUser,
                DisplayName = WellKnownRoleNames.Common_BasicUser
            });

            await userManager.CreateRoleAsync(new ApplicationRoleDTO
            {
                Id = WellKnownRoleIds.Client,
                Name = WellKnownRoleNames.Client,
                DisplayName = WellKnownRoleNames.Client
            });

            await userManager.CreateRoleAsync(new ApplicationRoleDTO
            {
                Id = _supportRoleId,
                Name = WellKnownRoleNames.Common_SupportUser,
                DisplayName = WellKnownRoleNames.Common_SupportUser
            });

            await userManager.CreateRoleAsync(new ApplicationRoleDTO
            {
                Id = _bahamasOwnerRoleId,
                Name = WellKnownRoleNames.Bahamas_Owner,
                DisplayName = WellKnownRoleNames.Bahamas_Owner
            });

            await userManager.CreateRoleAsync(new ApplicationRoleDTO
            {
                Id = _commonComOwner,
                Name = WellKnownRoleNames.Common_COM_Owner,
                DisplayName = WellKnownRoleNames.Common_COM_Owner
            });

            ManagementUser = await userManager.CreateUserWithRolesAsync(
                new RegistrationDTO
                {
                    LastName = "User 1",
                    FirstName = "Test",
                    UserName = "<EMAIL>",
                    DisplayName = "Test User 1",
                    Email = "<EMAIL>",
                    ObjectId = Guid.NewGuid(),
                    RoleIds = new List<Guid> { Guid.Parse("{A00D4E6C-DCD6-4FCA-B080-666EEB9AE6CE}") }
                }
            );

            ClientUser = await userManager.CreateUserWithRolesAsync(
                new RegistrationDTO
                {
                    LastName = "User 2",
                    Email = _clientUserEmail,
                    FirstName = "Test",
                    UserName = _clientUserEmail,
                    DisplayName = "Test User 2",
                    ObjectId = Guid.NewGuid(),
                    RoleIds = new List<Guid> { WellKnownRoleIds.Client }
                }
            );

            BasicUser = await userManager.CreateUserWithRolesAsync(
                    new RegistrationDTO
                    {
                        LastName = "User 3",
                        FirstName = "Test",
                        UserName = "<EMAIL>",
                        DisplayName = "Test User 3",
                        Email = "<EMAIL>",
                        ObjectId = Guid.NewGuid(),
                        RoleIds = new List<Guid> { _basicUserRoleId }
                    }
                );

            COMOwner = await userManager.CreateUserWithRolesAsync(
                new RegistrationDTO
                {
                    LastName = "User 4",
                    FirstName = "Test",
                    UserName = "<EMAIL>",
                    DisplayName = "Test User 4",
                    Email = "<EMAIL>",
                    ObjectId = Guid.NewGuid(),
                    RoleIds = new List<Guid> { _commonComOwner }
                }
                );

            _masterClient = new MasterClient()
            {
                Code = "TEST_123"
            };

            _masterClient.MasterClientUsers.Add(
                new MasterClientUser() { MasterClientId = _masterClient.Id, UserId = ClientUser.Id });

            await _server.Services.GetRequiredService<IMasterClientsRepository>().InsertAsync(_masterClient, true);
        }

        /// <summary>
        /// Setup of all modules.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task SetupModulesAsync()
        {
            await CreateModuleAsync(ModuleKeyConsts.BODirectors, "BO/Directors");

            // Nevis
            ModuleStrId = await CreateModuleAsync(ModuleKeyConsts.SimplifiedTaxReturn, "Simplified Tax Return");

            // Panama
            ModuleBfrId = await CreateModuleAsync(ModuleKeyConsts.BasicFinancialReportPanama, "Basic Financial Report");

            // Bahamas
            ModuleEsId = await CreateModuleAsync(ModuleKeyConsts.EconomicSubstanceBahamas, "Economic Substance");

            // BVI
            ModuleEsBviId = await CreateModuleAsync(ModuleKeyConsts.EconomicSubstanceBVI, "Economic Substance");
        }

        /// <summary>
        /// Creates the form templates for the various years.
        /// </summary>
        /// <returns></returns>
        private async Task CreateFormTemplatesAsync()
        {
            var years = new int[] { 2019, 2020, 2021, 2022, 2023, 2024 };

            var formsDataManager = _server.Services.GetRequiredService<IFormsDataManager>();
            await formsDataManager.CreateFormTemplatesForJurisdictionAsync(JurisdictionNevisId, ModuleKeyConsts.SimplifiedTaxReturn, years);
            await formsDataManager.CreateFormTemplatesForJurisdictionAsync(JurisdictionBahamasId, ModuleKeyConsts.EconomicSubstanceBahamas, years);
            await formsDataManager.CreateFormTemplatesForJurisdictionAsync(JurisdictionPanamaId, ModuleKeyConsts.BasicFinancialReportPanama, years);
        }

        /// <summary>
        /// Creates a module if it doesn't exist yet.
        /// </summary>
        /// <param name="key">The key for the module.</param>
        /// <param name="name">The name for the module.</param>
        /// <returns>The id of the module.</returns>
        private async Task<Guid> CreateModuleAsync(string key, string name)
        {
            var repository = _server.Services.GetRequiredService<IModulesRepository>();

            var existing = await repository.FindFirstOrDefaultByConditionAsync(x => x.Key == key);
            if (existing == null)
            {
                var toDb = new Module(Guid.NewGuid(), key, name);
                await repository.InsertAsync(toDb, saveChanges: true);
                return toDb.Id;
            }
            else
            {
                return existing.Id;
            }
        }
    }
}
