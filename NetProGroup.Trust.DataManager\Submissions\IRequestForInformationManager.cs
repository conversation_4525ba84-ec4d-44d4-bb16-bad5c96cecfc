// <copyright file="IRequestForInformationManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.RequestsForInformation;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Submissions
{
    /// <summary>
    /// Interface for the request for information manager.
    /// </summary>
    public interface IRequestForInformationManager : IScopedService
    {
        /// <summary>
        /// Searches for all submissions with created RFI requests.
        /// </summary>
        /// <param name="request">The necessary data to filter submissions with RFI requests created.</param>
        /// <returns>A paginated list of submissions with RFI requestes created.</returns>
        Task<IPagedList<ListSubmissionRFIDTO>> ListRequestForInformationAsync(ListRequestsForInformationRequest request);

        /// <summary>
        /// Searches for a request for information by its id.
        /// </summary>
        /// <param name="requestForInformationId">The id of the request for information as Guid.</param>
        /// <returns>A <see cref="RequestForInformationDTO"/> representing the request for information foundd.</returns>
        Task<RequestForInformationDTO> GetRequestForInformationByIdAsync(Guid requestForInformationId);

        /// <summary>
        /// Creates a request for information for a submission.
        /// </summary>
        /// <param name="submissionId">The id of the submission as Guid.</param>
        /// <param name="data">The data used to create the request for information.</param>
        /// <returns>A <see cref="Guid"/> representing the created entity Id.</returns>
        Task<Guid> CreateRequestForInformationAsync(Guid submissionId, CreateRFIDTO data);

        /// <summary>
        /// Retrieve the RFI a request for information for a submission.
        /// </summary>
        /// <param name="submissionId">The id of the submission as Guid.</param>
        /// <returns>A <see cref="Guid"/> representing the created entity Id.</returns>
        Task<SubmissionRFIDetailsDTO> GetRFISubmissionDetailsAsync(Guid submissionId);

        /// <summary>
        /// Creates a request for information document entity.
        /// </summary>
        /// <param name="requestForInformationId">The request for information id as Guid.</param>
        /// <param name="data">The necessary dataset used to create a request for  information document.</param>
        /// <param name="createdByManagementUser">Determine if the document was uploaded by a management user or not.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task CreateRFIDocumentAsync(Guid requestForInformationId, CreateRFIDocumentDTO data, bool createdByManagementUser);

        /// <summary>
        /// Mark a request for information entity as Cancelled by a management user.
        /// </summary>
        /// <param name="requestForInformationId">The request for information id as Guid.</param>
        /// <param name="reason">The cancellation reason.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task CancelRequestForInformationAsync(Guid requestForInformationId, string reason);

        /// <summary>
        /// Retrieve the a RFI request given its id.
        /// </summary>
        /// <param name="requestForInformationId">The request for information id as Guid.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<SubmissionRFIDTO> GetRFIById(Guid requestForInformationId);

        /// <summary>
        /// Completes a request for information for a submission (Client).
        /// </summary>
        /// <param name="requestForInformationId">The request for information id as Guid.</param>
        /// <param name="data">The data used to complete the request for information.</param>
        /// <returns>A <see cref="Guid"/> representing the created entity Id.</returns>
        Task CompleteRequestForInformationAsync(Guid requestForInformationId, CompleteRequestForInformationDTO data);

        /// <summary>
        /// Processes RFIs that are due in one week and sends notifications.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task ProcessRFIsDueInOneWeekAsync();

        /// <summary>
        /// Processes RFIs that are due in one day and sends notifications.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task ProcessRFIsDueInOneDayAsync();

        /// <summary>
        /// Processes RFIs that are three days overdue and sends notifications.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task ProcessRFIsThreeDaysOverdueAsync();
    }
}