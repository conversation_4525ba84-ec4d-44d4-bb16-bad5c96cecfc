// <copyright file="SubmissionRFIDetailsDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.Application.Contracts.Audits;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Application.Contracts.RequestsForInformation
{
    /// <summary>
    /// Represent the RFI details for a submission.
    /// </summary>
    public class SubmissionRFIDetailsDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the overall status of the submission.
        /// </summary>
        public SubmissionStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the requests for information related to the submission.
        /// </summary>
        public List<SubmissionRFIDTO> RequestsForInformation { get; set; }

        /// <summary>
        /// Gets or sets the paged list with ActivityLogItemDTO items.
        /// </summary>
        public List<ActivityLogItemDTO> ActivityLogItems { get; set; }
    }
}