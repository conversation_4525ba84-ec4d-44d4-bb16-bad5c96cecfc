﻿// <copyright file="IUsersAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Trust.Application.Contracts.Jurisdictions;
using NetProGroup.Trust.Application.Contracts.Users.MFA;
using NetProGroup.Trust.Application.Contracts.Users.TermsConditions;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.Users
{
    /// <summary>
    /// Interface for managing user-related operations within the application.
    /// </summary>
    public interface IUsersAppService : IScopedService
    {
        /// <summary>
        /// Gets the MFA info for the given user.
        /// </summary>
        /// <param name="userId">Id of the user to get the MFA info for.</param>
        /// <returns>A <see cref="Task{MFAInfoDTO}"/> representing the asynchronous operation holding the MFAInfo.</returns>
        Task<MFAInfoDTO> GetUserMFAInfoAsync(Guid userId);

        /// <summary>
        /// Gets the current MFA method for the user.
        /// </summary>
        /// <param name="userId">Id of the user to get the MFA method for.</param>
        /// <returns>A <see cref="Task{MFAInfoDTO}"/> representing the asynchronous operation holding the MFAInfo.</returns>
        Task<GetUserMFAMethodDTO> GetUserMFAMethodAsync(Guid userId);

        /// <summary>
        /// Sets the method for the MFA for the user.
        /// </summary>
        /// <param name="model">The DTO with the parameters to set the method.</param>
        /// <returns>A <see cref="Task{MFAInfoDTO}"/> representing the asynchronous operation holding the MFAInfo.</returns>
        Task<MFAInfoDTO> SetUserMFAMethodAsync(SetUserMFAMethodDTO model);

        /// <summary>
        /// Requests an email with the verification code. Not used for the authenticator method.
        /// </summary>
        /// <param name="userId">Id of the user to get the code for.</param>
        /// <returns>A <see cref="Task{MFAInfoDTO}"/> representing the asynchronous operation holding the MFAInfo.</returns>
        Task<MFAInfoDTO> RequestMFACodeByEmailAsync(Guid userId);

        /// <summary>
        /// Verifies the entered code from the user.
        /// </summary>
        /// <param name="model">The DTO with the parameters for verification.</param>
        /// <returns>A <see cref="Task{VerifyMFACodeResultDTO}"/> representing the asynchronous operation holding the result of the verification (true for succeeded).</returns>
        Task<VerifyMFACodeResultDTO> VerifyMFACodeAsync(VerifyMFACodeDTO model);

        /// <summary>
        /// Resets the MFA info for the given user.
        /// </summary>
        /// <param name="userId">Id of the user to reset the MFA info for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task ResetUserMFAInfoAsync(Guid userId);

        /// <summary>
        /// Request a reset for the MFA info for the given user.
        /// </summary>
        /// <param name="userId">Id of the user to reset the MFA for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<RequestMFAResetResultDTO> RequestUserMFAResetAsync(Guid userId);

        /// <summary>
        /// Confirms a reset for the MFA info for the given user.
        /// </summary>
        /// <param name="model">The DTO with the parameters for confirmation.</param>
        /// <returns>A <see cref="Task{ConfirmMFAResetResultDTO}"/> representing the asynchronous operation.</returns>
        Task<ConfirmMFAResetResultDTO> ConfirmUserMFAResetAsync(ConfirmMFAResetDTO model);

        /// <summary>
        /// Gets the Terms and Conditions acceptance status for a given user.
        /// </summary>
        /// <param name="userId">ID of the user to get the status for.</param>
        /// <returns>A <see cref="Task{TermsConditionsStatusDTO}"/> representing the asynchronous operation holding the acceptance status.</returns>
        Task<TermsConditionsStatusDTO> GetTermsConditionsStatusAsync(Guid userId);

        /// <summary>
        /// Sets the Terms and Conditions acceptance status for a given user.
        /// </summary>
        /// <param name="userId">ID of the user accepting the terms.</param>
        /// <param name="model">The acceptance model containing version information.</param>
        /// <returns>A <see cref="Task{AcceptTermsConditionsResultDTO}"/> representing the asynchronous operation holding the acceptance result.</returns>
        Task AcceptTermsConditionsAsync(Guid userId, AcceptTermsConditionsDTO model);

        /// <summary>
        /// Registration of the sign-in of an externalid user.
        /// </summary>
        /// <param name="emailAddress">The email address of the signed-in user.</param>
        /// <param name="objectId">The ObjectId of the user.</param>
        /// <returns>A <see cref="Task{ApplicationUserDTO}"/> representing the asynchronous operation holding the user.</returns>
        Task<PCPApplicationUserDTO> ExternalIdUserSignedInAsync(string emailAddress, Guid objectId);

        /// <summary>
        /// Registration of the sign-in of an entra user.
        /// </summary>
        /// <param name="objectId">The ObjectId of the user.</param>
        /// <returns>A <see cref="Task{ApplicationUserDTO}"/> representing the asynchronous operation holding the user.</returns>
        Task<PCPApplicationUserDTO> EntraUserSignedInAsync(Guid objectId);

        /// <summary>
        /// Registration of the sign-out of a user.
        /// </summary>
        /// <param name="userId">The id of the user.</param>
        /// <returns>A <see cref="Task{ApplicationUserDTO}"/> representing the asynchronous operation.</returns>
        Task UserSignedOutAsync(Guid userId);

        /// <summary>
        /// Gets all users.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>A list of <see cref="ApplicationUserDTO"/>.</returns>
        Task<IPagedList<ListApplicationUsersDTO>> ListUsersAsync(UsersRequestDTO request);

        /// <summary>
        /// Retrieves the details of a user by their unique identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the user.</param>
        /// <returns>
        /// An <see cref="ApplicationUserDTO"/> containing the details of the specified user.
        /// </returns>
        Task<PCPApplicationUserDTO> GetUserByIdAsync(Guid id);

        /// <summary>
        /// Block or unblock a user.
        /// </summary>
        /// <param name="id">The unique identifier of the user.</param>
        /// <param name="request">The request to block or unblock the user.</param>
        /// <returns>A value indicating whether the user was blocked or unblocked.</returns>
        Task<bool> BlockUnblockUserAsync(Guid id, BlockUserDTO request);

        /// <summary>
        /// Sets the masterclients for a user.
        /// </summary>
        /// <param name="userMasterClientsDTO">A new registration of a user.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SetUserMasterClientsAsync(UserMasterClientsDTO userMasterClientsDTO);

        /// <summary>
        /// Retrieves the permissions for a given user within the context of a jurisdiction.
        /// </summary>
        /// <param name="userId">The optional id of the user to get the permissions for. If null, the workcontext user is used.</param>
        /// <param name="jurisdictionId">The optional unique identifier of the jurisdiction.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a collection of user permissions.</returns>
        Task<IList<UserPermissionDTO>> GetPermissionsAsync(Guid? userId, Guid? jurisdictionId);

        /// <summary>
        /// Sends an invitation for the portal to the given user.
        /// </summary>
        /// <remarks>
        /// This does not check for an already sent invitation.
        /// </remarks>
        /// <param name="userId">Id of the user to send an invitation for.</param>
        /// <param name="masterClientId">Optional id of the masterclient to mention on the invitation.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SendInvitationAsync(Guid userId, Guid? masterClientId);

        /// <summary>
        /// Sends an invitation for the portal to the given users.
        /// </summary>
        /// <remarks>
        /// This does not check for an already sent invitation.
        /// </remarks>
        /// <param name="request">List of userids to create an invitation for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SendInvitationsAsync(SendInvitationsDTO request);

        /// <summary>
        /// Complements the user with the permission for that user.
        /// </summary>
        /// <param name="user">The user as PCPApplicationUserDTO.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task ComplementWithPermissionsAsync(PCPApplicationUserDTO user);

        /// <summary>
        /// Retrieves the permissions for the signed-in user within the context of a jurisdiction.
        /// </summary>
        /// <param name="jurisdictionId">The optional unique identifier of the jurisdiction.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a collection of user permissions.</returns>
        Task<IList<UserPermissionDTO>> GetPermissionsAsync(Guid? jurisdictionId);

        /// <summary>
        /// Gets the authorized jurisdictions for the current user.
        /// </summary>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the user authorized jurisdictions.</returns>
        Task<IList<JurisdictionDTO>> GetAuthorizedJurisdictionsAsync();

#if DEBUG
        /// <summary>
        /// Creates a new user using the parameters in createUserModel.
        /// </summary>
        /// <param name="createUserModel">A new registration of a user.</param>
        /// <returns>A <see cref="Task{ApplicationUserDTO}"/> representing the asynchronous operation.</returns>
        Task<ApplicationUserDTO> CreateUserAsync(CreateUserDTO createUserModel);
#endif
    }
}
