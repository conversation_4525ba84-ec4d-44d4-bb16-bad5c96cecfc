// <copyright file="ListSubmissionRFIDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Application.Contracts.RequestsForInformation
{
    /// <summary>
    /// Reprent a submission entity with a created request for information.
    /// </summary>
    public class ListSubmissionRFIDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the name of the legal entity.
        /// </summary>
        public string LegalEntityName { get; set; }

        /// <summary>
        /// Gets or sets the code of the legal entity.
        /// </summary>
        public string LegalEntityCode { get; set; }

        /// <summary>
        /// Gets or sets the master client code.
        /// </summary>
        public string MasterClientCode { get; set; }

        /// <summary>
        /// Gets or sets the start of the financial period.
        /// </summary>
        public DateTime FinancialPeriodStartsAt { get; set; }

        /// <summary>
        /// Gets or sets the end of the financial period.
        /// </summary>
        public DateTime FinancialPeriodEndsAt { get; set; }

        /// <summary>
        /// Gets or sets the overall status of the submission.
        /// </summary>
        public SubmissionStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the status of the request for information.
        /// </summary>
        public RequestForInformationStatus RFIStatus { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the submission was exported.
        /// </summary>
        /// <value>
        /// A nullable <see cref="DateTime"/> representing the export timestamp of the submission.
        /// If the submission is not exported, the value will be <c>null</c>.
        /// </value>
        public DateTime? ExportedAt { get; set; }

        /// <summary>
        /// Gets or sets the date and time that the RFI was created.
        /// </summary>
        public DateTime RFICreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the deadline date and time for the RFI.
        /// </summary>
        public DateTime RFIDeadLine { get; set; }

        /// <summary>
        /// Gets or sets the completed date and time for the RFI.
        /// </summary>
        public DateTime? RFICompletedAt { get; set; }

        /// <summary>
        /// Gets or sets the date and time of the last reminder sent for the RFI.
        /// </summary>
        public DateTime? RFILastReminderSentAt { get; set; }
    }
}