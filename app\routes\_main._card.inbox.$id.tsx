import { <PERSON><PERSON>, <PERSON><PERSON>, DialogContent } from "@netpro/design-system";
import type { ActionFunctionArgs, LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { <PERSON>, json, useF<PERSON>cher, use<PERSON>oader<PERSON><PERSON>, useNavigate } from "@remix-run/react";
import { type ReactNode, useEffect } from "react";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { middleware } from "~/lib/middlewares.server";
import type { GetInboxMessageResponse } from "~/services/api-generated";
import { createMessageReadStatus, getInboxMessage } from "~/services/api-generated";

export async function action({ request, params }: ActionFunctionArgs) {
  await middleware(["auth", "terms", "requireAnnouncements"], request);
  const session = await getSession(request.headers.get("<PERSON>ie"));
  const { id } = params;
  if (!id) {
    throw new Error("Message ID is required");
  }

  const { error } = await createMessageReadStatus({
    headers: await authHeaders(request),
    path: { messageId: id },
  });

  if (error) {
    session.flash("notification", { title: "Error", message: "Something went wrong updating the message as read", variant: "error" });

    return json({ success: false }, { headers: { "Set-Cookie": await commitSession(session) } });
  }

  session.flash("notification", { title: "Success", message: "Message marked as read", variant: "success" });

  return json({ success: true }, { headers: { "Set-Cookie": await commitSession(session) } });
}

export async function loader({ request, params }: LoaderFunctionArgs): Promise<TypedResponse<GetInboxMessageResponse>> {
  await middleware(["auth", "terms", "requireAnnouncements"], request);
  const { id } = params;
  if (!id) {
    throw new Error("Message ID is required");
  }

  const { data, error } = await getInboxMessage({
    headers: await authHeaders(request),
    path: { messageId: id },
  });

  if (error || !data) {
    throw new Response("Failed to fetch message", { status: 404 });
  }

  return json(data);
}

export default function InboxMessageDetails(): JSX.Element {
  const navigate = useNavigate();
  const message = useLoaderData<GetInboxMessageResponse>();
  const fetcher = useFetcher<{ success: boolean }>();
  const isSubmitting = fetcher.state === "submitting";

  function convertUrlsToLinks(text: string): ReactNode[] {
    // Combined regex to match URLs and emails
    const urlAndEmailRegex = /(https?:\/\/\S+|www\.\S+|[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:\/\S*)?|[\w.%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
    const emailRegex = /^[\w.%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i;
    const parts = text.split(urlAndEmailRegex);

    return parts.map((part, index) => {
      if (urlAndEmailRegex.test(part)) {
      // Check if it's an email
        if (emailRegex.test(part)) {
          return (
          // eslint-disable-next-line react/no-array-index-key
            <Button variant="link" key={index} className="p-0">
              <Link to={`mailto:${part}`}>
                {part}
              </Link>
            </Button>
          );
        }

        // Handle URLs
        const href = part.startsWith("http") ? part : `https://${part}`;

        return (
        // eslint-disable-next-line react/no-array-index-key
          <Button variant="link" key={index} className="p-0">
            <Link to={href} target="_blank" rel="noopener noreferrer">
              {part}
            </Link>
          </Button>
        );
      }

      return part;
    });
  }

  useEffect(() => {
    if (fetcher.data?.success) {
      navigate("/inbox");
    }
  }, [fetcher.data, navigate]);

  return (
    <Dialog open onOpenChange={() => navigate("/inbox")}>
      <DialogContent className="flex min-w-[800px] flex-col">
        <div className="text-lg font-semibold text-gray-800 mb-6">
          {message.subject}
        </div>
        <div className="border border-gray-300 rounded-md p-4 text-sm mb-6 max-h-[400px] overflow-auto">
          {message?.body?.split(/\r\n|\n/).map((line, index) => (
            // eslint-disable-next-line react/no-array-index-key
            <p key={index} className="mb-2">{convertUrlsToLinks(line)}</p>
          ))}
          {message.legalEntities && message.legalEntities.length > 0 && (
            <div className="mt-4">
              <strong>Affected companies:</strong>
              <ul className="list-disc list-inside">
                {message.legalEntities.map(entity => (
                  <li key={entity}>{entity}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
        {message.urlAttachments && message.urlAttachments.length > 0 && (
          <div className="mb-4">
            <div className="text-sm font-medium text-gray-600 mb-2">Attached URLs</div>
            <ul className="list-disc list-inside text-sm text-blue-600">
              {message.urlAttachments.map(attachment => (
                <li key={attachment}>
                  <a href={attachment} target="_blank" rel="noopener noreferrer">
                    {attachment}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        )}
        {message.inboxAttachments && message.inboxAttachments.length > 0 && (
          <div className="mb-6">
            <div className="text-sm font-medium text-gray-600 mb-2">Attachments</div>
            <ul className="list-disc list-inside text-sm text-blue-600">
              {message.inboxAttachments.map(attachment => (
                <li key={attachment.id}>
                  <a
                    href={`/api/download/${attachment.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {attachment.filename}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        )}
        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={() => navigate("/inbox")}>
            Close
          </Button>
          {!message.isRead && (
            <fetcher.Form method="post">
              <Button type="submit" disabled={isSubmitting}>
                Mark as read
              </Button>
            </fetcher.Form>
          )}
        </div>
      </DialogContent>
    </Dialog>

  );
}
